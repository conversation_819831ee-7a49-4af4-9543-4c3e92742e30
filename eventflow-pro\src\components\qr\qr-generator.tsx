'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Download, Mail, Smartphone, Copy, Check } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { generateQRCode, TicketData } from '@/lib/qr-generator'
import { toast } from 'sonner'

interface QRGeneratorProps {
  ticketData: TicketData
  onClose?: () => void
}

export default function QRGenerator({ ticketData, onClose }: QRGeneratorProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('')
  const [isGenerating, setIsGenerating] = useState(true)
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    generateTicketQR()
  }, [ticketData])

  const generateTicketQR = async () => {
    try {
      setIsGenerating(true)
      const qrUrl = await generateQRCode(ticketData)
      setQrCodeUrl(qrUrl)
    } catch (error) {
      console.error('Failed to generate QR code:', error)
      toast.error('Failed to generate QR code')
    } finally {
      setIsGenerating(false)
    }
  }

  const downloadQRCode = () => {
    if (!qrCodeUrl) return

    const link = document.createElement('a')
    link.href = qrCodeUrl
    link.download = `ticket-${ticketData.registrationId}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    toast.success('QR code downloaded successfully!')
  }

  const copyToClipboard = async () => {
    try {
      // Convert data URL to blob
      const response = await fetch(qrCodeUrl)
      const blob = await response.blob()
      
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ])
      
      setCopied(true)
      toast.success('QR code copied to clipboard!')
      
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      toast.error('Failed to copy QR code')
    }
  }

  const sendByEmail = () => {
    const subject = encodeURIComponent(`Your ticket for ${ticketData.eventTitle}`)
    const body = encodeURIComponent(`
Hi ${ticketData.attendeeName},

Your ticket for ${ticketData.eventTitle} is ready!

Please save this QR code and present it at the event entrance for quick check-in.

Event Details:
- Event: ${ticketData.eventTitle}
- Attendee: ${ticketData.attendeeName}
- Ticket ID: ${ticketData.registrationId}

See you at the event!

Best regards,
EventFlow Pro Team
    `)
    
    window.open(`mailto:${ticketData.attendeeEmail}?subject=${subject}&body=${body}`)
  }

  const addToWallet = () => {
    // In a real implementation, this would generate a wallet pass
    toast.info('Wallet integration coming soon!')
  }

  return (
    <div className="max-w-md mx-auto">
      <Card className="overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <CardTitle className="text-center">Event Ticket</CardTitle>
          <CardDescription className="text-blue-100 text-center">
            {ticketData.eventTitle}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="p-6">
          {/* Attendee Info */}
          <div className="text-center mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {ticketData.attendeeName}
            </h3>
            <p className="text-gray-600 text-sm">{ticketData.attendeeEmail}</p>
            <p className="text-gray-500 text-xs mt-1">
              Ticket ID: {ticketData.registrationId}
            </p>
          </div>

          {/* QR Code */}
          <div className="flex justify-center mb-6">
            {isGenerating ? (
              <div className="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full"
                />
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="relative group"
              >
                <img
                  src={qrCodeUrl}
                  alt="QR Code"
                  className="w-48 h-48 border border-gray-200 rounded-lg shadow-sm"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-lg flex items-center justify-center">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={copyToClipboard}
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  >
                    {copied ? (
                      <Check className="w-4 h-4 mr-1" />
                    ) : (
                      <Copy className="w-4 h-4 mr-1" />
                    )}
                    {copied ? 'Copied!' : 'Copy'}
                  </Button>
                </div>
              </motion.div>
            )}
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-blue-900 mb-2">Check-in Instructions:</h4>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• Present this QR code at the event entrance</li>
              <li>• Keep your phone screen bright for easy scanning</li>
              <li>• Arrive 15 minutes early for smooth check-in</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={downloadQRCode}
                variant="outline"
                className="flex items-center justify-center"
                disabled={isGenerating}
              >
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
              <Button
                onClick={copyToClipboard}
                variant="outline"
                className="flex items-center justify-center"
                disabled={isGenerating}
              >
                {copied ? (
                  <Check className="w-4 h-4 mr-2" />
                ) : (
                  <Copy className="w-4 h-4 mr-2" />
                )}
                {copied ? 'Copied!' : 'Copy'}
              </Button>
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={sendByEmail}
                variant="outline"
                className="flex items-center justify-center"
              >
                <Mail className="w-4 h-4 mr-2" />
                Email
              </Button>
              <Button
                onClick={addToWallet}
                variant="outline"
                className="flex items-center justify-center"
              >
                <Smartphone className="w-4 h-4 mr-2" />
                Add to Wallet
              </Button>
            </div>

            <Button
              onClick={onClose}
              variant="gradient"
              className="w-full"
            >
              Done
            </Button>
          </div>

          {/* Footer */}
          <div className="text-center mt-6 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Generated by EventFlow Pro<br />
              {new Date(ticketData.timestamp).toLocaleString()}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
