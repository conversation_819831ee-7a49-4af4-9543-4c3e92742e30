import QRCode from 'qrcode'
import { generateId } from './utils'

export interface TicketData {
  registrationId: string
  eventId: string
  attendeeId: string
  eventTitle: string
  attendeeName: string
  attendeeEmail: string
  timestamp: string
}

export async function generateQRCode(data: TicketData): Promise<string> {
  try {
    // Create a secure ticket payload
    const ticketPayload = {
      id: data.registrationId,
      eventId: data.eventId,
      attendeeId: data.attendeeId,
      timestamp: data.timestamp,
      // Add a simple hash for verification
      hash: generateTicketHash(data)
    }

    // Generate QR code as data URL
    const qrCodeDataURL = await QRCode.toDataURL(JSON.stringify(ticketPayload), {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: 256
    })

    return qrCodeDataURL
  } catch (error) {
    console.error('Error generating QR code:', error)
    throw new Error('Failed to generate QR code')
  }
}

export function generateTicketHash(data: TicketData): string {
  // Simple hash function for demo purposes
  // In production, use a proper cryptographic hash
  const payload = `${data.registrationId}-${data.eventId}-${data.attendeeId}-${data.timestamp}`
  let hash = 0
  for (let i = 0; i < payload.length; i++) {
    const char = payload.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36)
}

export function verifyTicketHash(data: TicketData, providedHash: string): boolean {
  const calculatedHash = generateTicketHash(data)
  return calculatedHash === providedHash
}

export async function generateTicketPDF(ticketData: TicketData, qrCodeDataURL: string): Promise<Blob> {
  // For now, we'll create a simple HTML-based ticket
  // In production, you might want to use a PDF library like jsPDF
  const ticketHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Event Ticket</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .ticket {
          background: white;
          border-radius: 12px;
          padding: 30px;
          max-width: 400px;
          margin: 0 auto;
          box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
          text-align: center;
          border-bottom: 2px dashed #e0e0e0;
          padding-bottom: 20px;
          margin-bottom: 20px;
        }
        .event-title {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          margin-bottom: 10px;
        }
        .attendee-info {
          margin-bottom: 20px;
        }
        .qr-section {
          text-align: center;
          margin-top: 20px;
        }
        .qr-code {
          max-width: 150px;
          height: auto;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="ticket">
        <div class="header">
          <div class="event-title">${ticketData.eventTitle}</div>
          <div style="color: #666;">Event Ticket</div>
        </div>
        
        <div class="attendee-info">
          <div><strong>Attendee:</strong> ${ticketData.attendeeName}</div>
          <div><strong>Email:</strong> ${ticketData.attendeeEmail}</div>
          <div><strong>Ticket ID:</strong> ${ticketData.registrationId}</div>
        </div>
        
        <div class="qr-section">
          <img src="${qrCodeDataURL}" alt="QR Code" class="qr-code" />
          <div style="margin-top: 10px; font-size: 12px; color: #666;">
            Scan this code at the event entrance
          </div>
        </div>
        
        <div class="footer">
          Generated by EventFlow Pro<br>
          ${new Date(ticketData.timestamp).toLocaleString()}
        </div>
      </div>
    </body>
    </html>
  `

  // Convert HTML to Blob (for download)
  return new Blob([ticketHTML], { type: 'text/html' })
}

export function parseQRCodeData(qrData: string): any {
  try {
    return JSON.parse(qrData)
  } catch (error) {
    console.error('Error parsing QR code data:', error)
    return null
  }
}

export function validateQRCodeData(parsedData: any): boolean {
  return (
    parsedData &&
    typeof parsedData === 'object' &&
    parsedData.id &&
    parsedData.eventId &&
    parsedData.attendeeId &&
    parsedData.timestamp &&
    parsedData.hash
  )
}
