'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Calendar,
  QrCode,
  BarChart3,
  Users,
  Clock,
  CheckCircle,
  Smartphone,
  Zap,
  TrendingUp,
  Shield,
  ArrowRight,
  Play
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function Home() {
  const [activeFeature, setActiveFeature] = useState(0)

  const features = [
    {
      icon: Calendar,
      title: "Smart Event Creation",
      description: "Drag-and-drop event builder with intelligent templates and automated scheduling suggestions.",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: QrCode,
      title: "QR Code Ticketing",
      description: "Instant ticket generation with secure QR codes, mobile wallet integration, and contactless check-in.",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: BarChart3,
      title: "AI-Powered Analytics",
      description: "Real-time insights, attendance predictions, and automated post-event reports with actionable recommendations.",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Smartphone,
      title: "Mobile-First Design",
      description: "Native app experience with offline functionality, push notifications, and seamless cross-device sync.",
      color: "from-orange-500 to-red-500"
    }
  ]

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Corporate Event Manager",
      company: "TechCorp Inc.",
      quote: "EventFlow Pro transformed our chaotic event planning into a streamlined process. We've reduced planning time by 70% and increased attendance by 40%.",
      before: "Managing 15 spreadsheets, constant email chains, manual check-ins taking 2 hours",
      after: "One unified dashboard, automated workflows, 5-minute check-in process"
    },
    {
      name: "Michael Rodriguez",
      role: "Conference Organizer",
      company: "Global Events Ltd.",
      quote: "The AI analytics helped us predict and prevent a 30% no-show rate. The insights are incredibly accurate and actionable.",
      before: "Guessing attendance, last-minute venue changes, poor engagement tracking",
      after: "95% attendance prediction accuracy, optimized venue selection, real-time engagement metrics"
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-200 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">EventFlow Pro</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">Features</a>
              <a href="/demo" className="text-gray-600 hover:text-gray-900 transition-colors">Demo</a>
              <a href="/dashboard" className="text-gray-600 hover:text-gray-900 transition-colors">Dashboard</a>
              <Button variant="outline">Sign In</Button>
              <Button variant="gradient" onClick={() => window.open('/demo', '_blank')}>
                Try Demo
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                From Event <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Chaos</span> to
                <br />Seamless <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">Execution</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Transform your corporate event management with AI-powered analytics, QR code ticketing,
                and real-time dashboards. Say goodbye to spreadsheet chaos and hello to professional automation.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button size="lg" variant="gradient" className="text-lg px-8 py-4" onClick={() => window.open('/demo', '_blank')}>
                  Start Your Transformation
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
                <Button size="lg" variant="outline" className="text-lg px-8 py-4" onClick={() => window.open('/demo', '_blank')}>
                  <Play className="mr-2 w-5 h-5" />
                  Try Interactive Demo
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Problem/Solution Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                The Reality of Corporate Event Management
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1">
                    <span className="text-red-600 text-sm">✗</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Spreadsheet Chaos</h3>
                    <p className="text-gray-600">Managing attendee lists across 15+ spreadsheets, constant version conflicts</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1">
                    <span className="text-red-600 text-sm">✗</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Manual Check-ins</h3>
                    <p className="text-gray-600">2-hour check-in queues, lost registrations, frustrated attendees</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1">
                    <span className="text-red-600 text-sm">✗</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">No Real Insights</h3>
                    <p className="text-gray-600">Guessing attendance, no engagement tracking, weeks of manual reporting</p>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                The EventFlow Pro Solution
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Unified Dashboard</h3>
                    <p className="text-gray-600">One platform for everything - registration, ticketing, analytics, and communication</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">5-Minute Check-ins</h3>
                    <p className="text-gray-600">QR code scanning, instant verification, seamless attendee experience</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">AI-Powered Insights</h3>
                    <p className="text-gray-600">Real-time analytics, attendance predictions, automated reports</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need for Perfect Events
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From creation to analytics, our platform handles every aspect of event management with intelligence and automation.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-600">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">EventFlow Pro</span>
              </div>
              <p className="text-gray-400">
                Transforming event management from chaos to seamless execution.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrations</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Status</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Security</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 EventFlow Pro. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
