'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Calendar, 
  Users, 
  TrendingUp, 
  Clock, 
  Plus,
  BarChart3,
  QrCode,
  MapPin,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatDate, getEventStatus, getStatusColor } from '@/lib/utils'

// Mock data for demonstration
const mockEvents = [
  {
    id: '1',
    title: 'Annual Tech Conference 2024',
    description: 'Join industry leaders for cutting-edge technology discussions',
    start_date: '2024-03-15T09:00:00Z',
    end_date: '2024-03-15T17:00:00Z',
    location: 'San Francisco Convention Center',
    venue_capacity: 500,
    status: 'published',
    registration_open: true,
    registrations: 342,
    checked_in: 0,
    cover_image_url: null
  },
  {
    id: '2',
    title: 'Product Launch Webinar',
    description: 'Introducing our revolutionary new platform features',
    start_date: '2024-02-28T14:00:00Z',
    end_date: '2024-02-28T15:30:00Z',
    location: 'Virtual Event',
    venue_capacity: 1000,
    status: 'published',
    registration_open: true,
    registrations: 756,
    checked_in: 0,
    cover_image_url: null
  },
  {
    id: '3',
    title: 'Team Building Workshop',
    description: 'Strengthen team bonds with interactive activities',
    start_date: '2024-01-20T10:00:00Z',
    end_date: '2024-01-20T16:00:00Z',
    location: 'Corporate Headquarters',
    venue_capacity: 50,
    status: 'completed',
    registration_open: false,
    registrations: 48,
    checked_in: 45,
    cover_image_url: null
  }
]

const stats = [
  {
    title: 'Total Events',
    value: '12',
    change: '+2 this month',
    icon: Calendar,
    color: 'text-blue-600'
  },
  {
    title: 'Total Attendees',
    value: '2,847',
    change: '+18% from last month',
    icon: Users,
    color: 'text-green-600'
  },
  {
    title: 'Avg. Attendance Rate',
    value: '87%',
    change: '+5% improvement',
    icon: TrendingUp,
    color: 'text-purple-600'
  },
  {
    title: 'Active Events',
    value: '3',
    change: 'Currently running',
    icon: Clock,
    color: 'text-orange-600'
  }
]

export default function Dashboard() {
  const [events, setEvents] = useState(mockEvents)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">EventFlow Pro</span>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline">
                <QrCode className="w-4 h-4 mr-2" />
                Scan QR
              </Button>
              <Button variant="gradient" onClick={() => window.open('/create-event', '_blank')}>
                <Plus className="w-4 h-4 mr-2" />
                Create Event
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Event Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage your events and track performance in real-time</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      <p className="text-sm text-gray-500 mt-1">{stat.change}</p>
                    </div>
                    <div className={`p-3 rounded-lg bg-gray-50 ${stat.color}`}>
                      <stat.icon className="w-6 h-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Events List */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Your Events</CardTitle>
                <CardDescription>Manage and monitor your event portfolio</CardDescription>
              </div>
              <Button variant="gradient" onClick={() => window.open('/create-event', '_blank')}>
                <Plus className="w-4 h-4 mr-2" />
                Create New Event
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {events.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{event.title}</h3>
                        <Badge 
                          variant={getEventStatus(event) === 'completed' ? 'success' : 
                                  getEventStatus(event) === 'live' ? 'warning' : 'info'}
                        >
                          {getEventStatus(event)}
                        </Badge>
                      </div>
                      <p className="text-gray-600 mb-3">{event.description}</p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(event.start_date)}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="w-4 h-4" />
                          <span>{event.location}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Users className="w-4 h-4" />
                          <span>{event.registrations}/{event.venue_capacity} registered</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <BarChart3 className="w-4 h-4 mr-1" />
                        Analytics
                      </Button>
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mt-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Registration Progress</span>
                      <span>{Math.round((event.registrations / event.venue_capacity) * 100)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min((event.registrations / event.venue_capacity) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
