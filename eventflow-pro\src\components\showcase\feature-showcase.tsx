'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Calendar, 
  QrCode, 
  BarChart3, 
  Smartphone,
  Users,
  Clock,
  CheckCircle,
  TrendingUp,
  Zap,
  Shield,
  Globe,
  Heart
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

const features = [
  {
    id: 'creation',
    title: 'Smart Event Creation',
    subtitle: 'AI-Powered Setup',
    description: 'Create professional events in minutes with our intelligent form builder and AI-powered templates.',
    icon: Calendar,
    color: 'from-blue-500 to-cyan-500',
    stats: [
      { label: 'Setup Time Reduced', value: '70%' },
      { label: 'Templates Available', value: '50+' },
      { label: 'AI Suggestions', value: '∞' }
    ],
    features: [
      'Drag-and-drop event builder',
      'Smart scheduling suggestions',
      'Automated workflow setup',
      'Template library',
      'AI content generation'
    ]
  },
  {
    id: 'ticketing',
    title: 'QR Code Ticketing',
    subtitle: 'Contactless & Secure',
    description: 'Generate secure QR code tickets instantly with mobile wallet integration and anti-fraud protection.',
    icon: QrCode,
    color: 'from-purple-500 to-pink-500',
    stats: [
      { label: 'Check-in Speed', value: '5min' },
      { label: 'Security Level', value: '99.9%' },
      { label: 'Mobile Compatible', value: '100%' }
    ],
    features: [
      'Instant QR code generation',
      'Mobile wallet integration',
      'Offline scanning capability',
      'Real-time verification',
      'Anti-fraud security'
    ]
  },
  {
    id: 'analytics',
    title: 'AI-Powered Analytics',
    subtitle: 'Predictive Insights',
    description: 'Get real-time insights, attendance predictions, and automated reports with our advanced AI engine.',
    icon: BarChart3,
    color: 'from-green-500 to-emerald-500',
    stats: [
      { label: 'Prediction Accuracy', value: '95%' },
      { label: 'Real-time Updates', value: 'Live' },
      { label: 'Report Generation', value: 'Auto' }
    ],
    features: [
      'Live attendance tracking',
      'Predictive analytics',
      'Automated insights',
      'Industry benchmarking',
      'Risk assessment'
    ]
  },
  {
    id: 'mobile',
    title: 'Mobile-First Design',
    subtitle: 'Native App Experience',
    description: 'Optimized for mobile devices with offline functionality, push notifications, and seamless performance.',
    icon: Smartphone,
    color: 'from-orange-500 to-red-500',
    stats: [
      { label: 'Mobile Users', value: '85%' },
      { label: 'Offline Support', value: 'Yes' },
      { label: 'Load Time', value: '<1s' }
    ],
    features: [
      'Progressive Web App',
      'Offline functionality',
      'Push notifications',
      'Camera integration',
      'Cross-device sync'
    ]
  }
]

const benefits = [
  {
    icon: Users,
    title: 'Increased Attendance',
    description: '40% higher attendance rates with streamlined registration',
    metric: '+40%'
  },
  {
    icon: Clock,
    title: 'Time Savings',
    description: '70% reduction in event planning and setup time',
    metric: '70%'
  },
  {
    icon: TrendingUp,
    title: 'Better Insights',
    description: '95% accuracy in attendance predictions and analytics',
    metric: '95%'
  },
  {
    icon: Shield,
    title: 'Enhanced Security',
    description: 'Enterprise-grade security with fraud prevention',
    metric: '99.9%'
  }
]

export default function FeatureShowcase() {
  const [activeFeature, setActiveFeature] = useState(features[0])
  const [hoveredBenefit, setHoveredBenefit] = useState<number | null>(null)

  return (
    <div className="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Badge variant="info" className="mb-4">
              Platform Features
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything You Need for
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                {" "}Perfect Events
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From intelligent event creation to AI-powered analytics, our platform handles every aspect 
              of event management with precision and automation.
            </p>
          </motion.div>
        </div>

        {/* Feature Navigation */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {features.map((feature, index) => (
            <motion.button
              key={feature.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              onClick={() => setActiveFeature(feature)}
              className={`flex items-center space-x-3 px-6 py-4 rounded-xl border transition-all duration-300 ${
                activeFeature.id === feature.id
                  ? 'bg-white border-blue-500 shadow-lg scale-105'
                  : 'bg-white/50 border-gray-200 hover:border-gray-300 hover:shadow-md'
              }`}
            >
              <div className={`p-2 rounded-lg bg-gradient-to-r ${feature.color}`}>
                <feature.icon className="w-5 h-5 text-white" />
              </div>
              <div className="text-left">
                <div className="font-semibold text-gray-900">{feature.title}</div>
                <div className="text-sm text-gray-600">{feature.subtitle}</div>
              </div>
            </motion.button>
          ))}
        </div>

        {/* Active Feature Display */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeFeature.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.6 }}
            className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden"
          >
            <div className="grid lg:grid-cols-2 gap-8 p-8">
              {/* Feature Details */}
              <div className="space-y-6">
                <div>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${activeFeature.color}`}>
                      <activeFeature.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900">{activeFeature.title}</h3>
                      <p className="text-blue-600 font-medium">{activeFeature.subtitle}</p>
                    </div>
                  </div>
                  <p className="text-gray-600 text-lg leading-relaxed">
                    {activeFeature.description}
                  </p>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4">
                  {activeFeature.stats.map((stat, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      className="text-center p-4 bg-gray-50 rounded-lg"
                    >
                      <div className="text-2xl font-bold text-blue-600">{stat.value}</div>
                      <div className="text-sm text-gray-600">{stat.label}</div>
                    </motion.div>
                  ))}
                </div>

                {/* Feature List */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Key Capabilities:</h4>
                  <div className="space-y-2">
                    {activeFeature.features.map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 }}
                        className="flex items-center space-x-3"
                      >
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <span className="text-gray-700">{feature}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Visual Representation */}
              <div className="flex items-center justify-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8 }}
                  className={`w-64 h-64 rounded-2xl bg-gradient-to-br ${activeFeature.color} flex items-center justify-center relative overflow-hidden`}
                >
                  <activeFeature.icon className="w-32 h-32 text-white/20" />
                  <motion.div
                    animate={{ 
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{ 
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute inset-0 bg-white/10 rounded-2xl"
                  />
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3">
                      <div className="text-white font-semibold text-sm">
                        {activeFeature.title}
                      </div>
                      <div className="text-white/80 text-xs">
                        {activeFeature.subtitle}
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Benefits Section */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Measurable Business Impact
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our platform delivers quantifiable improvements across all aspects of event management.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                onHoverStart={() => setHoveredBenefit(index)}
                onHoverEnd={() => setHoveredBenefit(null)}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 border-2 hover:border-blue-200">
                  <CardContent className="p-6 text-center">
                    <motion.div
                      animate={{ 
                        scale: hoveredBenefit === index ? 1.1 : 1,
                        rotate: hoveredBenefit === index ? 5 : 0
                      }}
                      transition={{ duration: 0.3 }}
                      className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4"
                    >
                      <benefit.icon className="w-8 h-8 text-white" />
                    </motion.div>
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {benefit.metric}
                    </div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {benefit.title}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <Heart className="w-12 h-12 mx-auto mb-4 text-pink-200" />
            <h3 className="text-2xl font-bold mb-4">
              Ready to Transform Your Events?
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Join thousands of event organizers who've already made the switch from chaos to seamless execution.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary">
                Start Free Trial
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-gray-900">
                Schedule Demo
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
