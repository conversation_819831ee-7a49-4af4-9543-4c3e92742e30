{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatDateShort(date: string | Date) {\n  return new Intl.DateTimeFormat('en-US', {\n    month: 'short',\n    day: 'numeric',\n    year: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function generateId() {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n\nexport function truncateText(text: string, maxLength: number) {\n  if (text.length <= maxLength) return text\n  return text.substring(0, maxLength) + '...'\n}\n\nexport function calculateEventDuration(startDate: string, endDate: string) {\n  const start = new Date(startDate)\n  const end = new Date(endDate)\n  const diffMs = end.getTime() - start.getTime()\n  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))\n  const diffDays = Math.floor(diffHours / 24)\n  \n  if (diffDays > 0) {\n    return `${diffDays} day${diffDays > 1 ? 's' : ''}`\n  } else {\n    return `${diffHours} hour${diffHours > 1 ? 's' : ''}`\n  }\n}\n\nexport function getEventStatus(event: {\n  start_date: string\n  end_date: string\n  status: string\n}) {\n  const now = new Date()\n  const startDate = new Date(event.start_date)\n  const endDate = new Date(event.end_date)\n  \n  if (event.status === 'cancelled') return 'cancelled'\n  if (event.status === 'draft') return 'draft'\n  if (now < startDate) return 'upcoming'\n  if (now >= startDate && now <= endDate) return 'live'\n  if (now > endDate) return 'completed'\n  \n  return event.status\n}\n\nexport function getStatusColor(status: string) {\n  switch (status) {\n    case 'draft':\n      return 'bg-gray-100 text-gray-800'\n    case 'upcoming':\n      return 'bg-blue-100 text-blue-800'\n    case 'live':\n      return 'bg-green-100 text-green-800'\n    case 'completed':\n      return 'bg-purple-100 text-purple-800'\n    case 'cancelled':\n      return 'bg-red-100 text-red-800'\n    default:\n      return 'bg-gray-100 text-gray-800'\n  }\n}\n\nexport function validateEmail(email: string) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,gBAAgB,IAAmB;IACjD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,OAAO;QACP,KAAK;QACL,MAAM;IACR,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,uBAAuB,SAAiB,EAAE,OAAe;IACvE,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,SAAS,IAAI,OAAO,KAAK,MAAM,OAAO;IAC5C,MAAM,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,EAAE;IACrD,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;IAExC,IAAI,WAAW,GAAG;QAChB,OAAO,GAAG,SAAS,IAAI,EAAE,WAAW,IAAI,MAAM,IAAI;IACpD,OAAO;QACL,OAAO,GAAG,UAAU,KAAK,EAAE,YAAY,IAAI,MAAM,IAAI;IACvD;AACF;AAEO,SAAS,eAAe,KAI9B;IACC,MAAM,MAAM,IAAI;IAChB,MAAM,YAAY,IAAI,KAAK,MAAM,UAAU;IAC3C,MAAM,UAAU,IAAI,KAAK,MAAM,QAAQ;IAEvC,IAAI,MAAM,MAAM,KAAK,aAAa,OAAO;IACzC,IAAI,MAAM,MAAM,KAAK,SAAS,OAAO;IACrC,IAAI,MAAM,WAAW,OAAO;IAC5B,IAAI,OAAO,aAAa,OAAO,SAAS,OAAO;IAC/C,IAAI,MAAM,SAAS,OAAO;IAE1B,OAAO,MAAM,MAAM;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        gradient: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning:\n          \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        info:\n          \"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/components/qr/qr-scanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { Camera, X, CheckCircle, AlertCircle, Scan } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { parseQRCodeData, validateQRCodeData } from '@/lib/qr-generator'\n\ninterface QRScannerProps {\n  onScanSuccess: (data: any) => void\n  onScanError: (error: string) => void\n  isOpen: boolean\n  onClose: () => void\n}\n\ninterface ScanResult {\n  success: boolean\n  data?: any\n  error?: string\n  attendeeName?: string\n  eventTitle?: string\n  timestamp?: string\n}\n\nexport default function QRScanner({ onScanSuccess, onScanError, isOpen, onClose }: QRScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResult, setScanResult] = useState<ScanResult | null>(null)\n  const [hasPermission, setHasPermission] = useState<boolean | null>(null)\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const canvasRef = useRef<HTMLCanvasElement>(null)\n  const streamRef = useRef<MediaStream | null>(null)\n\n  // Mock scan results for demo purposes\n  const mockScanResults = [\n    {\n      success: true,\n      data: { id: 'reg_001', eventId: 'evt_001', attendeeId: 'att_001' },\n      attendeeName: 'John Smith',\n      eventTitle: 'Annual Tech Conference 2024',\n      timestamp: new Date().toISOString()\n    },\n    {\n      success: true,\n      data: { id: 'reg_002', eventId: 'evt_001', attendeeId: 'att_002' },\n      attendeeName: 'Sarah Johnson',\n      eventTitle: 'Annual Tech Conference 2024',\n      timestamp: new Date().toISOString()\n    },\n    {\n      success: false,\n      error: 'Invalid QR code format',\n      timestamp: new Date().toISOString()\n    }\n  ]\n\n  useEffect(() => {\n    if (isOpen) {\n      requestCameraPermission()\n    } else {\n      stopCamera()\n    }\n\n    return () => {\n      stopCamera()\n    }\n  }, [isOpen])\n\n  const requestCameraPermission = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        video: { \n          facingMode: 'environment',\n          width: { ideal: 1280 },\n          height: { ideal: 720 }\n        } \n      })\n      \n      setHasPermission(true)\n      streamRef.current = stream\n      \n      if (videoRef.current) {\n        videoRef.current.srcObject = stream\n        videoRef.current.play()\n      }\n    } catch (error) {\n      console.error('Camera permission denied:', error)\n      setHasPermission(false)\n    }\n  }\n\n  const stopCamera = () => {\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop())\n      streamRef.current = null\n    }\n    setIsScanning(false)\n  }\n\n  const simulateScan = () => {\n    setIsScanning(true)\n    \n    // Simulate scanning delay\n    setTimeout(() => {\n      const randomResult = mockScanResults[Math.floor(Math.random() * mockScanResults.length)]\n      setScanResult(randomResult)\n      setIsScanning(false)\n      \n      if (randomResult.success) {\n        onScanSuccess(randomResult.data)\n      } else {\n        onScanError(randomResult.error || 'Scan failed')\n      }\n    }, 2000)\n  }\n\n  const resetScan = () => {\n    setScanResult(null)\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <motion.div\n        initial={{ opacity: 0, scale: 0.9 }}\n        animate={{ opacity: 1, scale: 1 }}\n        exit={{ opacity: 0, scale: 0.9 }}\n        className=\"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-hidden\"\n      >\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">QR Code Scanner</h2>\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"w-5 h-5\" />\n            </Button>\n          </div>\n\n          {hasPermission === null && (\n            <div className=\"text-center py-8\">\n              <Camera className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600\">Requesting camera permission...</p>\n            </div>\n          )}\n\n          {hasPermission === false && (\n            <div className=\"text-center py-8\">\n              <AlertCircle className=\"w-12 h-12 text-red-500 mx-auto mb-4\" />\n              <p className=\"text-gray-600 mb-4\">Camera permission is required to scan QR codes</p>\n              <Button onClick={requestCameraPermission}>\n                Try Again\n              </Button>\n            </div>\n          )}\n\n          {hasPermission === true && !scanResult && (\n            <div className=\"space-y-4\">\n              {/* Camera Preview */}\n              <div className=\"relative bg-gray-900 rounded-lg overflow-hidden aspect-video\">\n                <video\n                  ref={videoRef}\n                  className=\"w-full h-full object-cover\"\n                  playsInline\n                  muted\n                />\n                \n                {/* Scanning Overlay */}\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"relative\">\n                    <div className=\"w-48 h-48 border-2 border-white rounded-lg relative\">\n                      {/* Corner indicators */}\n                      <div className=\"absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-blue-500\"></div>\n                      <div className=\"absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-blue-500\"></div>\n                      <div className=\"absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-blue-500\"></div>\n                      <div className=\"absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-blue-500\"></div>\n                      \n                      {/* Scanning line animation */}\n                      {isScanning && (\n                        <motion.div\n                          className=\"absolute left-0 right-0 h-0.5 bg-blue-500\"\n                          animate={{ y: [0, 192, 0] }}\n                          transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                        />\n                      )}\n                    </div>\n                    \n                    <p className=\"text-white text-center mt-4\">\n                      {isScanning ? 'Scanning...' : 'Position QR code within the frame'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Scan Button */}\n              <Button \n                onClick={simulateScan} \n                disabled={isScanning}\n                className=\"w-full\"\n                variant=\"gradient\"\n                size=\"lg\"\n              >\n                {isScanning ? (\n                  <>\n                    <motion.div\n                      animate={{ rotate: 360 }}\n                      transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                      className=\"mr-2\"\n                    >\n                      <Scan className=\"w-5 h-5\" />\n                    </motion.div>\n                    Scanning...\n                  </>\n                ) : (\n                  <>\n                    <Scan className=\"w-5 h-5 mr-2\" />\n                    Scan QR Code\n                  </>\n                )}\n              </Button>\n            </div>\n          )}\n\n          {/* Scan Result */}\n          {scanResult && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"space-y-4\"\n            >\n              <Card className={`border-2 ${scanResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>\n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    {scanResult.success ? (\n                      <CheckCircle className=\"w-6 h-6 text-green-600\" />\n                    ) : (\n                      <AlertCircle className=\"w-6 h-6 text-red-600\" />\n                    )}\n                    <CardTitle className={scanResult.success ? 'text-green-800' : 'text-red-800'}>\n                      {scanResult.success ? 'Check-in Successful!' : 'Scan Failed'}\n                    </CardTitle>\n                  </div>\n                </CardHeader>\n                <CardContent>\n                  {scanResult.success ? (\n                    <div className=\"space-y-2\">\n                      <div>\n                        <span className=\"font-medium text-green-800\">Attendee:</span>\n                        <span className=\"ml-2 text-green-700\">{scanResult.attendeeName}</span>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-green-800\">Event:</span>\n                        <span className=\"ml-2 text-green-700\">{scanResult.eventTitle}</span>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-green-800\">Time:</span>\n                        <span className=\"ml-2 text-green-700\">\n                          {new Date(scanResult.timestamp!).toLocaleTimeString()}\n                        </span>\n                      </div>\n                      <Badge variant=\"success\" className=\"mt-2\">\n                        Checked In\n                      </Badge>\n                    </div>\n                  ) : (\n                    <div>\n                      <p className=\"text-red-700\">{scanResult.error}</p>\n                      <p className=\"text-red-600 text-sm mt-1\">\n                        Please ensure the QR code is valid and try again.\n                      </p>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              <div className=\"flex space-x-3\">\n                <Button onClick={resetScan} variant=\"outline\" className=\"flex-1\">\n                  Scan Another\n                </Button>\n                <Button onClick={onClose} variant=\"gradient\" className=\"flex-1\">\n                  Done\n                </Button>\n              </div>\n            </motion.div>\n          )}\n        </div>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AA0Be,SAAS,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAkB;;IAC/F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAE7C,sCAAsC;IACtC,MAAM,kBAAkB;QACtB;YACE,SAAS;YACT,MAAM;gBAAE,IAAI;gBAAW,SAAS;gBAAW,YAAY;YAAU;YACjE,cAAc;YACd,YAAY;YACZ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA;YACE,SAAS;YACT,MAAM;gBAAE,IAAI;gBAAW,SAAS;gBAAW,YAAY;YAAU;YACjE,cAAc;YACd,YAAY;YACZ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA;YACE,SAAS;YACT,OAAO;YACP,WAAW,IAAI,OAAO,WAAW;QACnC;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,QAAQ;gBACV;YACF,OAAO;gBACL;YACF;YAEA;uCAAO;oBACL;gBACF;;QACF;8BAAG;QAAC;KAAO;IAEX,MAAM,0BAA0B;QAC9B,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBACL,YAAY;oBACZ,OAAO;wBAAE,OAAO;oBAAK;oBACrB,QAAQ;wBAAE,OAAO;oBAAI;gBACvB;YACF;YAEA,iBAAiB;YACjB,UAAU,OAAO,GAAG;YAEpB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAC7B,SAAS,OAAO,CAAC,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YACzD,UAAU,OAAO,GAAG;QACtB;QACA,cAAc;IAChB;IAEA,MAAM,eAAe;QACnB,cAAc;QAEd,0BAA0B;QAC1B,WAAW;YACT,MAAM,eAAe,eAAe,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,gBAAgB,MAAM,EAAE;YACxF,cAAc;YACd,cAAc;YAEd,IAAI,aAAa,OAAO,EAAE;gBACxB,cAAc,aAAa,IAAI;YACjC,OAAO;gBACL,YAAY,aAAa,KAAK,IAAI;YACpC;QACF,GAAG;IACL;IAEA,MAAM,YAAY;QAChB,cAAc;IAChB;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,WAAU;sBAEV,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;0CACzC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAIhB,kBAAkB,sBACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAIhC,kBAAkB,uBACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;0CAAyB;;;;;;;;;;;;oBAM7C,kBAAkB,QAAQ,CAAC,4BAC1B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK;wCACL,WAAU;wCACV,WAAW;wCACX,KAAK;;;;;;kDAIP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;wDAGd,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,GAAG;oEAAC;oEAAG;oEAAK;iEAAE;4DAAC;4DAC1B,YAAY;gEAAE,UAAU;gEAAG,QAAQ;gEAAU,MAAM;4DAAS;;;;;;;;;;;;8DAKlE,6LAAC;oDAAE,WAAU;8DACV,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAOtC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;gCACV,SAAQ;gCACR,MAAK;0CAEJ,2BACC;;sDACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ;4CAAI;4CACvB,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAS;4CAC5D,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;wCACL;;iEAIf;;sDACE,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;oBAS1C,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAW,CAAC,SAAS,EAAE,WAAW,OAAO,GAAG,iCAAiC,4BAA4B;;kDAC7G,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,WAAW,OAAO,iBACjB,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DAEzB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAW,WAAW,OAAO,GAAG,mBAAmB;8DAC3D,WAAW,OAAO,GAAG,yBAAyB;;;;;;;;;;;;;;;;;kDAIrD,6LAAC,mIAAA,CAAA,cAAW;kDACT,WAAW,OAAO,iBACjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;sEAC7C,6LAAC;4DAAK,WAAU;sEAAuB,WAAW,YAAY;;;;;;;;;;;;8DAEhE,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;sEAC7C,6LAAC;4DAAK,WAAU;sEAAuB,WAAW,UAAU;;;;;;;;;;;;8DAE9D,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;sEAC7C,6LAAC;4DAAK,WAAU;sEACb,IAAI,KAAK,WAAW,SAAS,EAAG,kBAAkB;;;;;;;;;;;;8DAGvD,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAO;;;;;;;;;;;iEAK5C,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAgB,WAAW,KAAK;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;0CAQjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAW,SAAQ;wCAAU,WAAU;kDAAS;;;;;;kDAGjE,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAS,SAAQ;wCAAW,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhF;GAvQwB;KAAA", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/lib/qr-generator.ts"], "sourcesContent": ["import QRCode from 'qrcode'\nimport { generateId } from './utils'\n\nexport interface TicketData {\n  registrationId: string\n  eventId: string\n  attendeeId: string\n  eventTitle: string\n  attendeeName: string\n  attendeeEmail: string\n  timestamp: string\n}\n\nexport async function generateQRCode(data: TicketData): Promise<string> {\n  try {\n    // Create a secure ticket payload\n    const ticketPayload = {\n      id: data.registrationId,\n      eventId: data.eventId,\n      attendeeId: data.attendeeId,\n      timestamp: data.timestamp,\n      // Add a simple hash for verification\n      hash: generateTicketHash(data)\n    }\n\n    // Generate QR code as data URL\n    const qrCodeDataURL = await QRCode.toDataURL(JSON.stringify(ticketPayload), {\n      errorCorrectionLevel: 'M',\n      type: 'image/png',\n      quality: 0.92,\n      margin: 1,\n      color: {\n        dark: '#000000',\n        light: '#FFFFFF'\n      },\n      width: 256\n    })\n\n    return qrCodeDataURL\n  } catch (error) {\n    console.error('Error generating QR code:', error)\n    throw new Error('Failed to generate QR code')\n  }\n}\n\nexport function generateTicketHash(data: TicketData): string {\n  // Simple hash function for demo purposes\n  // In production, use a proper cryptographic hash\n  const payload = `${data.registrationId}-${data.eventId}-${data.attendeeId}-${data.timestamp}`\n  let hash = 0\n  for (let i = 0; i < payload.length; i++) {\n    const char = payload.charCodeAt(i)\n    hash = ((hash << 5) - hash) + char\n    hash = hash & hash // Convert to 32-bit integer\n  }\n  return Math.abs(hash).toString(36)\n}\n\nexport function verifyTicketHash(data: TicketData, providedHash: string): boolean {\n  const calculatedHash = generateTicketHash(data)\n  return calculatedHash === providedHash\n}\n\nexport async function generateTicketPDF(ticketData: TicketData, qrCodeDataURL: string): Promise<Blob> {\n  // For now, we'll create a simple HTML-based ticket\n  // In production, you might want to use a PDF library like jsPDF\n  const ticketHTML = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>Event Ticket</title>\n      <style>\n        body {\n          font-family: Arial, sans-serif;\n          margin: 0;\n          padding: 20px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        }\n        .ticket {\n          background: white;\n          border-radius: 12px;\n          padding: 30px;\n          max-width: 400px;\n          margin: 0 auto;\n          box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n        }\n        .header {\n          text-align: center;\n          border-bottom: 2px dashed #e0e0e0;\n          padding-bottom: 20px;\n          margin-bottom: 20px;\n        }\n        .event-title {\n          font-size: 24px;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 10px;\n        }\n        .attendee-info {\n          margin-bottom: 20px;\n        }\n        .qr-section {\n          text-align: center;\n          margin-top: 20px;\n        }\n        .qr-code {\n          max-width: 150px;\n          height: auto;\n        }\n        .footer {\n          text-align: center;\n          margin-top: 20px;\n          font-size: 12px;\n          color: #666;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"ticket\">\n        <div class=\"header\">\n          <div class=\"event-title\">${ticketData.eventTitle}</div>\n          <div style=\"color: #666;\">Event Ticket</div>\n        </div>\n        \n        <div class=\"attendee-info\">\n          <div><strong>Attendee:</strong> ${ticketData.attendeeName}</div>\n          <div><strong>Email:</strong> ${ticketData.attendeeEmail}</div>\n          <div><strong>Ticket ID:</strong> ${ticketData.registrationId}</div>\n        </div>\n        \n        <div class=\"qr-section\">\n          <img src=\"${qrCodeDataURL}\" alt=\"QR Code\" class=\"qr-code\" />\n          <div style=\"margin-top: 10px; font-size: 12px; color: #666;\">\n            Scan this code at the event entrance\n          </div>\n        </div>\n        \n        <div class=\"footer\">\n          Generated by EventFlow Pro<br>\n          ${new Date(ticketData.timestamp).toLocaleString()}\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n\n  // Convert HTML to Blob (for download)\n  return new Blob([ticketHTML], { type: 'text/html' })\n}\n\nexport function parseQRCodeData(qrData: string): any {\n  try {\n    return JSON.parse(qrData)\n  } catch (error) {\n    console.error('Error parsing QR code data:', error)\n    return null\n  }\n}\n\nexport function validateQRCodeData(parsedData: any): boolean {\n  return (\n    parsedData &&\n    typeof parsedData === 'object' &&\n    parsedData.id &&\n    parsedData.eventId &&\n    parsedData.attendeeId &&\n    parsedData.timestamp &&\n    parsedData.hash\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAaO,eAAe,eAAe,IAAgB;IACnD,IAAI;QACF,iCAAiC;QACjC,MAAM,gBAAgB;YACpB,IAAI,KAAK,cAAc;YACvB,SAAS,KAAK,OAAO;YACrB,YAAY,KAAK,UAAU;YAC3B,WAAW,KAAK,SAAS;YACzB,qCAAqC;YACrC,MAAM,mBAAmB;QAC3B;QAEA,+BAA+B;QAC/B,MAAM,gBAAgB,MAAM,2IAAA,CAAA,UAAM,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC,gBAAgB;YAC1E,sBAAsB;YACtB,MAAM;YACN,SAAS;YACT,QAAQ;YACR,OAAO;gBACL,MAAM;gBACN,OAAO;YACT;YACA,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,SAAS,mBAAmB,IAAgB;IACjD,yCAAyC;IACzC,iDAAiD;IACjD,MAAM,UAAU,GAAG,KAAK,cAAc,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;IAC7F,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,OAAO,QAAQ,UAAU,CAAC;QAChC,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ;QAC9B,OAAO,OAAO,KAAK,4BAA4B;;IACjD;IACA,OAAO,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;AACjC;AAEO,SAAS,iBAAiB,IAAgB,EAAE,YAAoB;IACrE,MAAM,iBAAiB,mBAAmB;IAC1C,OAAO,mBAAmB;AAC5B;AAEO,eAAe,kBAAkB,UAAsB,EAAE,aAAqB;IACnF,mDAAmD;IACnD,gEAAgE;IAChE,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAuDa,EAAE,WAAW,UAAU,CAAC;;;;;0CAKjB,EAAE,WAAW,YAAY,CAAC;uCAC7B,EAAE,WAAW,aAAa,CAAC;2CACvB,EAAE,WAAW,cAAc,CAAC;;;;oBAInD,EAAE,cAAc;;;;;;;;UAQ1B,EAAE,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc,GAAG;;;;;EAK1D,CAAC;IAED,sCAAsC;IACtC,OAAO,IAAI,KAAK;QAAC;KAAW,EAAE;QAAE,MAAM;IAAY;AACpD;AAEO,SAAS,gBAAgB,MAAc;IAC5C,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAEO,SAAS,mBAAmB,UAAe;IAChD,OACE,cACA,OAAO,eAAe,YACtB,WAAW,EAAE,IACb,WAAW,OAAO,IAClB,WAAW,UAAU,IACrB,WAAW,SAAS,IACpB,WAAW,IAAI;AAEnB", "debugId": null}}, {"offset": {"line": 1128, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/components/qr/qr-generator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { Download, Mail, Smartphone, Copy, Check } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { generateQRCode, TicketData } from '@/lib/qr-generator'\nimport { toast } from 'sonner'\n\ninterface QRGeneratorProps {\n  ticketData: TicketData\n  onClose?: () => void\n}\n\nexport default function QRGenerator({ ticketData, onClose }: QRGeneratorProps) {\n  const [qrCodeUrl, setQrCodeUrl] = useState<string>('')\n  const [isGenerating, setIsGenerating] = useState(true)\n  const [copied, setCopied] = useState(false)\n\n  useEffect(() => {\n    generateTicketQR()\n  }, [ticketData])\n\n  const generateTicketQR = async () => {\n    try {\n      setIsGenerating(true)\n      const qrUrl = await generateQRCode(ticketData)\n      setQrCodeUrl(qrUrl)\n    } catch (error) {\n      console.error('Failed to generate QR code:', error)\n      toast.error('Failed to generate QR code')\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const downloadQRCode = () => {\n    if (!qrCodeUrl) return\n\n    const link = document.createElement('a')\n    link.href = qrCodeUrl\n    link.download = `ticket-${ticketData.registrationId}.png`\n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n    \n    toast.success('QR code downloaded successfully!')\n  }\n\n  const copyToClipboard = async () => {\n    try {\n      // Convert data URL to blob\n      const response = await fetch(qrCodeUrl)\n      const blob = await response.blob()\n      \n      await navigator.clipboard.write([\n        new ClipboardItem({ 'image/png': blob })\n      ])\n      \n      setCopied(true)\n      toast.success('QR code copied to clipboard!')\n      \n      setTimeout(() => setCopied(false), 2000)\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error)\n      toast.error('Failed to copy QR code')\n    }\n  }\n\n  const sendByEmail = () => {\n    const subject = encodeURIComponent(`Your ticket for ${ticketData.eventTitle}`)\n    const body = encodeURIComponent(`\nHi ${ticketData.attendeeName},\n\nYour ticket for ${ticketData.eventTitle} is ready!\n\nPlease save this QR code and present it at the event entrance for quick check-in.\n\nEvent Details:\n- Event: ${ticketData.eventTitle}\n- Attendee: ${ticketData.attendeeName}\n- Ticket ID: ${ticketData.registrationId}\n\nSee you at the event!\n\nBest regards,\nEventFlow Pro Team\n    `)\n    \n    window.open(`mailto:${ticketData.attendeeEmail}?subject=${subject}&body=${body}`)\n  }\n\n  const addToWallet = () => {\n    // In a real implementation, this would generate a wallet pass\n    toast.info('Wallet integration coming soon!')\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto\">\n      <Card className=\"overflow-hidden\">\n        <CardHeader className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n          <CardTitle className=\"text-center\">Event Ticket</CardTitle>\n          <CardDescription className=\"text-blue-100 text-center\">\n            {ticketData.eventTitle}\n          </CardDescription>\n        </CardHeader>\n        \n        <CardContent className=\"p-6\">\n          {/* Attendee Info */}\n          <div className=\"text-center mb-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n              {ticketData.attendeeName}\n            </h3>\n            <p className=\"text-gray-600 text-sm\">{ticketData.attendeeEmail}</p>\n            <p className=\"text-gray-500 text-xs mt-1\">\n              Ticket ID: {ticketData.registrationId}\n            </p>\n          </div>\n\n          {/* QR Code */}\n          <div className=\"flex justify-center mb-6\">\n            {isGenerating ? (\n              <div className=\"w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center\">\n                <motion.div\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                  className=\"w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full\"\n                />\n              </div>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5 }}\n                className=\"relative group\"\n              >\n                <img\n                  src={qrCodeUrl}\n                  alt=\"QR Code\"\n                  className=\"w-48 h-48 border border-gray-200 rounded-lg shadow-sm\"\n                />\n                <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-lg flex items-center justify-center\">\n                  <Button\n                    variant=\"secondary\"\n                    size=\"sm\"\n                    onClick={copyToClipboard}\n                    className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                  >\n                    {copied ? (\n                      <Check className=\"w-4 h-4 mr-1\" />\n                    ) : (\n                      <Copy className=\"w-4 h-4 mr-1\" />\n                    )}\n                    {copied ? 'Copied!' : 'Copy'}\n                  </Button>\n                </div>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Instructions */}\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\">\n            <h4 className=\"font-medium text-blue-900 mb-2\">Check-in Instructions:</h4>\n            <ul className=\"text-blue-800 text-sm space-y-1\">\n              <li>• Present this QR code at the event entrance</li>\n              <li>• Keep your phone screen bright for easy scanning</li>\n              <li>• Arrive 15 minutes early for smooth check-in</li>\n            </ul>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"space-y-3\">\n            <div className=\"grid grid-cols-2 gap-3\">\n              <Button\n                onClick={downloadQRCode}\n                variant=\"outline\"\n                className=\"flex items-center justify-center\"\n                disabled={isGenerating}\n              >\n                <Download className=\"w-4 h-4 mr-2\" />\n                Download\n              </Button>\n              <Button\n                onClick={copyToClipboard}\n                variant=\"outline\"\n                className=\"flex items-center justify-center\"\n                disabled={isGenerating}\n              >\n                {copied ? (\n                  <Check className=\"w-4 h-4 mr-2\" />\n                ) : (\n                  <Copy className=\"w-4 h-4 mr-2\" />\n                )}\n                {copied ? 'Copied!' : 'Copy'}\n              </Button>\n            </div>\n            \n            <div className=\"grid grid-cols-2 gap-3\">\n              <Button\n                onClick={sendByEmail}\n                variant=\"outline\"\n                className=\"flex items-center justify-center\"\n              >\n                <Mail className=\"w-4 h-4 mr-2\" />\n                Email\n              </Button>\n              <Button\n                onClick={addToWallet}\n                variant=\"outline\"\n                className=\"flex items-center justify-center\"\n              >\n                <Smartphone className=\"w-4 h-4 mr-2\" />\n                Add to Wallet\n              </Button>\n            </div>\n\n            <Button\n              onClick={onClose}\n              variant=\"gradient\"\n              className=\"w-full\"\n            >\n              Done\n            </Button>\n          </div>\n\n          {/* Footer */}\n          <div className=\"text-center mt-6 pt-4 border-t border-gray-200\">\n            <p className=\"text-xs text-gray-500\">\n              Generated by EventFlow Pro<br />\n              {new Date(ticketData.timestamp).toLocaleString()}\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAee,SAAS,YAAY,EAAE,UAAU,EAAE,OAAO,EAAoB;;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;KAAW;IAEf,MAAM,mBAAmB;QACvB,IAAI;YACF,gBAAgB;YAChB,MAAM,QAAQ,MAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;YACnC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW;QAEhB,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,OAAO,EAAE,WAAW,cAAc,CAAC,IAAI,CAAC;QACzD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,2BAA2B;YAC3B,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;gBAC9B,IAAI,cAAc;oBAAE,aAAa;gBAAK;aACvC;YAED,UAAU;YACV,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,UAAU,mBAAmB,CAAC,gBAAgB,EAAE,WAAW,UAAU,EAAE;QAC7E,MAAM,OAAO,mBAAmB,CAAC;GAClC,EAAE,WAAW,YAAY,CAAC;;gBAEb,EAAE,WAAW,UAAU,CAAC;;;;;SAK/B,EAAE,WAAW,UAAU,CAAC;YACrB,EAAE,WAAW,YAAY,CAAC;aACzB,EAAE,WAAW,cAAc,CAAC;;;;;;IAMrC,CAAC;QAED,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,aAAa,CAAC,SAAS,EAAE,QAAQ,MAAM,EAAE,MAAM;IAClF;IAEA,MAAM,cAAc;QAClB,8DAA8D;QAC9D,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAc;;;;;;sCACnC,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB,WAAW,UAAU;;;;;;;;;;;;8BAI1B,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,WAAW,YAAY;;;;;;8CAE1B,6LAAC;oCAAE,WAAU;8CAAyB,WAAW,aAAa;;;;;;8CAC9D,6LAAC;oCAAE,WAAU;;wCAA6B;wCAC5B,WAAW,cAAc;;;;;;;;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;sCACZ,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;oCAC5D,WAAU;;;;;;;;;;qDAId,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC;wCACC,KAAK;wCACL,KAAI;wCACJ,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;gDAET,uBACC,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;yEAEjB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAEjB,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;sCAQhC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAKR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAQ;4CACR,WAAU;4CACV,UAAU;;8DAEV,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAQ;4CACR,WAAU;4CACV,UAAU;;gDAET,uBACC,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;yEAEjB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAEjB,SAAS,YAAY;;;;;;;;;;;;;8CAI1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAQ;4CACR,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAQ;4CACR,WAAU;;8DAEV,6LAAC,iNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAK3C,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAAwB;kDACT,6LAAC;;;;;oCAC1B,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D;GA9NwB;KAAA", "debugId": null}}, {"offset": {"line": 1611, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/lib/ai-analytics.ts"], "sourcesContent": ["export interface EventAnalytics {\n  totalRegistrations: number\n  checkedInCount: number\n  attendanceRate: number\n  registrationTrend: Array<{ date: string; count: number }>\n  checkInTrend: Array<{ time: string; count: number }>\n  demographicData: {\n    ageGroups: Array<{ group: string; count: number }>\n    locations: Array<{ location: string; count: number }>\n  }\n  engagementMetrics: {\n    averageSessionDuration: number\n    peakAttendanceTime: string\n    dropOffRate: number\n  }\n}\n\nexport interface AIInsights {\n  attendancePrediction: {\n    predicted: number\n    confidence: number\n    factors: string[]\n  }\n  recommendations: Array<{\n    type: 'marketing' | 'logistics' | 'engagement' | 'timing'\n    priority: 'high' | 'medium' | 'low'\n    title: string\n    description: string\n    impact: string\n  }>\n  riskFactors: Array<{\n    factor: string\n    severity: 'high' | 'medium' | 'low'\n    mitigation: string\n  }>\n  benchmarkComparison: {\n    industryAverage: number\n    yourPerformance: number\n    percentile: number\n  }\n}\n\nexport async function generateEventAnalytics(eventId: string): Promise<EventAnalytics> {\n  // In a real implementation, this would fetch data from your database\n  // For demo purposes, we'll generate realistic mock data\n  \n  const mockAnalytics: EventAnalytics = {\n    totalRegistrations: Math.floor(Math.random() * 500) + 100,\n    checkedInCount: Math.floor(Math.random() * 400) + 80,\n    attendanceRate: 0,\n    registrationTrend: generateRegistrationTrend(),\n    checkInTrend: generateCheckInTrend(),\n    demographicData: {\n      ageGroups: [\n        { group: '18-25', count: Math.floor(Math.random() * 50) + 20 },\n        { group: '26-35', count: Math.floor(Math.random() * 80) + 40 },\n        { group: '36-45', count: Math.floor(Math.random() * 60) + 30 },\n        { group: '46-55', count: Math.floor(Math.random() * 40) + 20 },\n        { group: '55+', count: Math.floor(Math.random() * 30) + 10 }\n      ],\n      locations: [\n        { location: 'New York', count: Math.floor(Math.random() * 100) + 50 },\n        { location: 'California', count: Math.floor(Math.random() * 80) + 40 },\n        { location: 'Texas', count: Math.floor(Math.random() * 60) + 30 },\n        { location: 'Florida', count: Math.floor(Math.random() * 50) + 25 },\n        { location: 'Other', count: Math.floor(Math.random() * 70) + 35 }\n      ]\n    },\n    engagementMetrics: {\n      averageSessionDuration: Math.floor(Math.random() * 120) + 60, // minutes\n      peakAttendanceTime: '2:30 PM',\n      dropOffRate: Math.random() * 0.3 + 0.1 // 10-40%\n    }\n  }\n\n  mockAnalytics.attendanceRate = (mockAnalytics.checkedInCount / mockAnalytics.totalRegistrations) * 100\n\n  return mockAnalytics\n}\n\nexport async function generateAIInsights(analytics: EventAnalytics): Promise<AIInsights> {\n  // Simulate AI analysis with realistic insights\n  const insights: AIInsights = {\n    attendancePrediction: {\n      predicted: Math.floor(analytics.totalRegistrations * (0.8 + Math.random() * 0.2)),\n      confidence: Math.floor(Math.random() * 20) + 75, // 75-95%\n      factors: [\n        'Historical attendance patterns',\n        'Weather forecast',\n        'Competing events',\n        'Registration timing',\n        'Marketing reach'\n      ]\n    },\n    recommendations: generateRecommendations(analytics),\n    riskFactors: generateRiskFactors(analytics),\n    benchmarkComparison: {\n      industryAverage: 78.5,\n      yourPerformance: analytics.attendanceRate,\n      percentile: Math.floor(Math.random() * 40) + 60 // 60-100th percentile\n    }\n  }\n\n  return insights\n}\n\nfunction generateRegistrationTrend(): Array<{ date: string; count: number }> {\n  const trend = []\n  const startDate = new Date()\n  startDate.setDate(startDate.getDate() - 30)\n\n  for (let i = 0; i < 30; i++) {\n    const date = new Date(startDate)\n    date.setDate(date.getDate() + i)\n    \n    // Simulate increasing registrations closer to event\n    const baseCount = Math.floor(Math.random() * 10) + 1\n    const proximityMultiplier = (30 - i) / 30 * 2 + 0.5\n    const count = Math.floor(baseCount * proximityMultiplier)\n    \n    trend.push({\n      date: date.toISOString().split('T')[0],\n      count\n    })\n  }\n\n  return trend\n}\n\nfunction generateCheckInTrend(): Array<{ time: string; count: number }> {\n  const trend = []\n  const eventStart = 9 // 9 AM\n  const eventEnd = 17 // 5 PM\n\n  for (let hour = eventStart; hour <= eventEnd; hour++) {\n    const time = `${hour}:00`\n    let count = 0\n\n    // Simulate check-in patterns\n    if (hour === eventStart) {\n      count = Math.floor(Math.random() * 50) + 30 // Heavy check-in at start\n    } else if (hour === eventStart + 1) {\n      count = Math.floor(Math.random() * 30) + 20 // Continued check-ins\n    } else if (hour >= 12 && hour <= 13) {\n      count = Math.floor(Math.random() * 20) + 10 // Lunch break arrivals\n    } else {\n      count = Math.floor(Math.random() * 15) + 5 // Scattered throughout\n    }\n\n    trend.push({ time, count })\n  }\n\n  return trend\n}\n\nfunction generateRecommendations(analytics: EventAnalytics): AIInsights['recommendations'] {\n  const recommendations = []\n\n  if (analytics.attendanceRate < 70) {\n    recommendations.push({\n      type: 'marketing',\n      priority: 'high',\n      title: 'Boost Last-Minute Marketing',\n      description: 'Current attendance rate is below optimal. Consider targeted social media campaigns and email reminders.',\n      impact: 'Could increase attendance by 15-25%'\n    })\n  }\n\n  if (analytics.engagementMetrics.dropOffRate > 0.25) {\n    recommendations.push({\n      type: 'engagement',\n      priority: 'medium',\n      title: 'Improve Session Engagement',\n      description: 'High drop-off rate detected. Consider interactive elements, Q&A sessions, or networking breaks.',\n      impact: 'Reduce drop-off by 10-15%'\n    })\n  }\n\n  recommendations.push({\n    type: 'logistics',\n    priority: 'medium',\n    title: 'Optimize Check-in Process',\n    description: 'Based on registration patterns, prepare for peak check-in times around event start.',\n    impact: 'Reduce wait times by 40%'\n  })\n\n  return recommendations\n}\n\nfunction generateRiskFactors(analytics: EventAnalytics): AIInsights['riskFactors'] {\n  const risks = []\n\n  if (analytics.totalRegistrations > 400) {\n    risks.push({\n      factor: 'High Registration Volume',\n      severity: 'medium',\n      mitigation: 'Ensure adequate staffing and multiple check-in stations'\n    })\n  }\n\n  if (analytics.attendanceRate < 60) {\n    risks.push({\n      factor: 'Low Predicted Attendance',\n      severity: 'high',\n      mitigation: 'Implement targeted re-engagement campaigns and follow-up communications'\n    })\n  }\n\n  return risks\n}\n\nexport function calculateROI(eventCost: number, revenue: number, attendeeValue: number): number {\n  const totalValue = revenue + (attendeeValue * 0.1) // Assume 10% of attendee value is immediate\n  return ((totalValue - eventCost) / eventCost) * 100\n}\n\nexport function predictOptimalEventTime(historicalData: any[]): string {\n  // Analyze historical data to suggest optimal event timing\n  // For demo, return a recommendation\n  return \"Tuesday-Thursday, 2:00 PM - 4:00 PM shows highest engagement rates\"\n}\n"], "names": [], "mappings": ";;;;;;AA0CO,eAAe,uBAAuB,OAAe;IAC1D,qEAAqE;IACrE,wDAAwD;IAExD,MAAM,gBAAgC;QACpC,oBAAoB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QACtD,gBAAgB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QAClD,gBAAgB;QAChB,mBAAmB;QACnB,cAAc;QACd,iBAAiB;YACf,WAAW;gBACT;oBAAE,OAAO;oBAAS,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAAG;gBAC7D;oBAAE,OAAO;oBAAS,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAAG;gBAC7D;oBAAE,OAAO;oBAAS,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAAG;gBAC7D;oBAAE,OAAO;oBAAS,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAAG;gBAC7D;oBAAE,OAAO;oBAAO,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAAG;aAC5D;YACD,WAAW;gBACT;oBAAE,UAAU;oBAAY,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;gBAAG;gBACpE;oBAAE,UAAU;oBAAc,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAAG;gBACrE;oBAAE,UAAU;oBAAS,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAAG;gBAChE;oBAAE,UAAU;oBAAW,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAAG;gBAClE;oBAAE,UAAU;oBAAS,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAAG;aACjE;QACH;QACA,mBAAmB;YACjB,wBAAwB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;YAC1D,oBAAoB;YACpB,aAAa,KAAK,MAAM,KAAK,MAAM,IAAI,SAAS;QAClD;IACF;IAEA,cAAc,cAAc,GAAG,AAAC,cAAc,cAAc,GAAG,cAAc,kBAAkB,GAAI;IAEnG,OAAO;AACT;AAEO,eAAe,mBAAmB,SAAyB;IAChE,+CAA+C;IAC/C,MAAM,WAAuB;QAC3B,sBAAsB;YACpB,WAAW,KAAK,KAAK,CAAC,UAAU,kBAAkB,GAAG,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;YAC/E,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;YAC7C,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,iBAAiB,wBAAwB;QACzC,aAAa,oBAAoB;QACjC,qBAAqB;YACnB,iBAAiB;YACjB,iBAAiB,UAAU,cAAc;YACzC,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,sBAAsB;QACxE;IACF;IAEA,OAAO;AACT;AAEA,SAAS;IACP,MAAM,QAAQ,EAAE;IAChB,MAAM,YAAY,IAAI;IACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;IAExC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,MAAM,OAAO,IAAI,KAAK;QACtB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;QAE9B,oDAAoD;QACpD,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;QACnD,MAAM,sBAAsB,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI;QAChD,MAAM,QAAQ,KAAK,KAAK,CAAC,YAAY;QAErC,MAAM,IAAI,CAAC;YACT,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS;IACP,MAAM,QAAQ,EAAE;IAChB,MAAM,aAAa,EAAE,OAAO;;IAC5B,MAAM,WAAW,GAAG,OAAO;;IAE3B,IAAK,IAAI,OAAO,YAAY,QAAQ,UAAU,OAAQ;QACpD,MAAM,OAAO,GAAG,KAAK,GAAG,CAAC;QACzB,IAAI,QAAQ;QAEZ,6BAA6B;QAC7B,IAAI,SAAS,YAAY;YACvB,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,0BAA0B;;QACxE,OAAO,IAAI,SAAS,aAAa,GAAG;YAClC,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,sBAAsB;;QACpE,OAAO,IAAI,QAAQ,MAAM,QAAQ,IAAI;YACnC,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,uBAAuB;;QACrE,OAAO;YACL,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,EAAE,uBAAuB;;QACpE;QAEA,MAAM,IAAI,CAAC;YAAE;YAAM;QAAM;IAC3B;IAEA,OAAO;AACT;AAEA,SAAS,wBAAwB,SAAyB;IACxD,MAAM,kBAAkB,EAAE;IAE1B,IAAI,UAAU,cAAc,GAAG,IAAI;QACjC,gBAAgB,IAAI,CAAC;YACnB,MAAM;YACN,UAAU;YACV,OAAO;YACP,aAAa;YACb,QAAQ;QACV;IACF;IAEA,IAAI,UAAU,iBAAiB,CAAC,WAAW,GAAG,MAAM;QAClD,gBAAgB,IAAI,CAAC;YACnB,MAAM;YACN,UAAU;YACV,OAAO;YACP,aAAa;YACb,QAAQ;QACV;IACF;IAEA,gBAAgB,IAAI,CAAC;QACnB,MAAM;QACN,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;IACV;IAEA,OAAO;AACT;AAEA,SAAS,oBAAoB,SAAyB;IACpD,MAAM,QAAQ,EAAE;IAEhB,IAAI,UAAU,kBAAkB,GAAG,KAAK;QACtC,MAAM,IAAI,CAAC;YACT,QAAQ;YACR,UAAU;YACV,YAAY;QACd;IACF;IAEA,IAAI,UAAU,cAAc,GAAG,IAAI;QACjC,MAAM,IAAI,CAAC;YACT,QAAQ;YACR,UAAU;YACV,YAAY;QACd;IACF;IAEA,OAAO;AACT;AAEO,SAAS,aAAa,SAAiB,EAAE,OAAe,EAAE,aAAqB;IACpF,MAAM,aAAa,UAAW,gBAAgB,IAAK,4CAA4C;;IAC/F,OAAO,AAAC,CAAC,aAAa,SAAS,IAAI,YAAa;AAClD;AAEO,SAAS,wBAAwB,cAAqB;IAC3D,0DAA0D;IAC1D,oCAAoC;IACpC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1819, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/components/analytics/analytics-dashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  TrendingUp, \n  Users, \n  Clock, \n  Target,\n  Brain,\n  AlertTriangle,\n  CheckCircle,\n  BarChart3,\n  PieChart,\n  Activity\n} from 'lucide-react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { \n  LineChart, \n  Line, \n  AreaChart, \n  Area, \n  BarChart, \n  Bar, \n  PieChart as RechartsPieChart, \n  Cell, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer,\n  Legend\n} from 'recharts'\nimport { generateEventAnalytics, generateAIInsights, EventAnalytics, AIInsights } from '@/lib/ai-analytics'\n\ninterface AnalyticsDashboardProps {\n  eventId: string\n  eventTitle: string\n}\n\nconst COLORS = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444']\n\nexport default function AnalyticsDashboard({ eventId, eventTitle }: AnalyticsDashboardProps) {\n  const [analytics, setAnalytics] = useState<EventAnalytics | null>(null)\n  const [insights, setInsights] = useState<AIInsights | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [activeTab, setActiveTab] = useState<'overview' | 'insights' | 'predictions'>('overview')\n\n  useEffect(() => {\n    loadAnalytics()\n  }, [eventId])\n\n  const loadAnalytics = async () => {\n    try {\n      setIsLoading(true)\n      const analyticsData = await generateEventAnalytics(eventId)\n      const insightsData = await generateAIInsights(analyticsData)\n      \n      setAnalytics(analyticsData)\n      setInsights(insightsData)\n    } catch (error) {\n      console.error('Failed to load analytics:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <motion.div\n          animate={{ rotate: 360 }}\n          transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n          className=\"w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full\"\n        />\n      </div>\n    )\n  }\n\n  if (!analytics || !insights) {\n    return (\n      <div className=\"text-center py-8\">\n        <AlertTriangle className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n        <p className=\"text-gray-600\">Failed to load analytics data</p>\n        <Button onClick={loadAnalytics} className=\"mt-4\">\n          Try Again\n        </Button>\n      </div>\n    )\n  }\n\n  const keyMetrics = [\n    {\n      title: 'Total Registrations',\n      value: analytics.totalRegistrations.toLocaleString(),\n      change: '+12% vs last event',\n      icon: Users,\n      color: 'text-blue-600'\n    },\n    {\n      title: 'Checked In',\n      value: analytics.checkedInCount.toLocaleString(),\n      change: `${analytics.attendanceRate.toFixed(1)}% attendance rate`,\n      icon: CheckCircle,\n      color: 'text-green-600'\n    },\n    {\n      title: 'Avg. Session Duration',\n      value: `${analytics.engagementMetrics.averageSessionDuration} min`,\n      change: '+8% vs industry avg',\n      icon: Clock,\n      color: 'text-purple-600'\n    },\n    {\n      title: 'Peak Attendance',\n      value: analytics.engagementMetrics.peakAttendanceTime,\n      change: 'Optimal timing',\n      icon: TrendingUp,\n      color: 'text-orange-600'\n    }\n  ]\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">Analytics Dashboard</h2>\n          <p className=\"text-gray-600\">{eventTitle}</p>\n        </div>\n        <div className=\"flex space-x-2\">\n          {(['overview', 'insights', 'predictions'] as const).map((tab) => (\n            <Button\n              key={tab}\n              variant={activeTab === tab ? 'default' : 'outline'}\n              onClick={() => setActiveTab(tab)}\n              className=\"capitalize\"\n            >\n              {tab}\n            </Button>\n          ))}\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {keyMetrics.map((metric, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: index * 0.1 }}\n          >\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">{metric.title}</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{metric.value}</p>\n                    <p className=\"text-sm text-gray-500 mt-1\">{metric.change}</p>\n                  </div>\n                  <div className={`p-3 rounded-lg bg-gray-50 ${metric.color}`}>\n                    <metric.icon className=\"w-6 h-6\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'overview' && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Registration Trend */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <BarChart3 className=\"w-5 h-5 mr-2\" />\n                Registration Trend\n              </CardTitle>\n              <CardDescription>Daily registration count over time</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <AreaChart data={analytics.registrationTrend}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"date\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Area \n                    type=\"monotone\" \n                    dataKey=\"count\" \n                    stroke=\"#3B82F6\" \n                    fill=\"#3B82F6\" \n                    fillOpacity={0.3}\n                  />\n                </AreaChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n\n          {/* Check-in Pattern */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Activity className=\"w-5 h-5 mr-2\" />\n                Check-in Pattern\n              </CardTitle>\n              <CardDescription>Hourly check-in distribution</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart data={analytics.checkInTrend}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"time\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Bar dataKey=\"count\" fill=\"#10B981\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n\n          {/* Demographics */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <PieChart className=\"w-5 h-5 mr-2\" />\n                Age Demographics\n              </CardTitle>\n              <CardDescription>Attendee age distribution</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <RechartsPieChart>\n                  <Pie\n                    data={analytics.demographicData.ageGroups}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    outerRadius={80}\n                    dataKey=\"count\"\n                    nameKey=\"group\"\n                  >\n                    {analytics.demographicData.ageGroups.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                  <Legend />\n                </RechartsPieChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n\n          {/* Geographic Distribution */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Target className=\"w-5 h-5 mr-2\" />\n                Geographic Distribution\n              </CardTitle>\n              <CardDescription>Attendee locations</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {analytics.demographicData.locations.map((location, index) => (\n                  <div key={location.location} className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium\">{location.location}</span>\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"w-24 bg-gray-200 rounded-full h-2\">\n                        <div\n                          className=\"bg-blue-600 h-2 rounded-full\"\n                          style={{\n                            width: `${(location.count / Math.max(...analytics.demographicData.locations.map(l => l.count))) * 100}%`\n                          }}\n                        />\n                      </div>\n                      <span className=\"text-sm text-gray-600 w-8\">{location.count}</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {activeTab === 'insights' && (\n        <div className=\"space-y-6\">\n          {/* AI Recommendations */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Brain className=\"w-5 h-5 mr-2\" />\n                AI-Powered Recommendations\n              </CardTitle>\n              <CardDescription>Actionable insights to improve your event</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {insights.recommendations.map((rec, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\n                    className=\"border border-gray-200 rounded-lg p-4\"\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <h4 className=\"font-semibold text-gray-900\">{rec.title}</h4>\n                          <Badge \n                            variant={rec.priority === 'high' ? 'destructive' : \n                                   rec.priority === 'medium' ? 'warning' : 'secondary'}\n                          >\n                            {rec.priority} priority\n                          </Badge>\n                        </div>\n                        <p className=\"text-gray-600 text-sm mb-2\">{rec.description}</p>\n                        <p className=\"text-green-600 text-sm font-medium\">\n                          Expected Impact: {rec.impact}\n                        </p>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Risk Factors */}\n          {insights.riskFactors.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <AlertTriangle className=\"w-5 h-5 mr-2\" />\n                  Risk Assessment\n                </CardTitle>\n                <CardDescription>Potential issues and mitigation strategies</CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {insights.riskFactors.map((risk, index) => (\n                    <div key={index} className=\"border border-orange-200 bg-orange-50 rounded-lg p-4\">\n                      <div className=\"flex items-start space-x-3\">\n                        <AlertTriangle className=\"w-5 h-5 text-orange-600 mt-0.5\" />\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2 mb-1\">\n                            <h4 className=\"font-semibold text-orange-900\">{risk.factor}</h4>\n                            <Badge variant={risk.severity === 'high' ? 'destructive' : 'warning'}>\n                              {risk.severity} risk\n                            </Badge>\n                          </div>\n                          <p className=\"text-orange-800 text-sm\">\n                            <strong>Mitigation:</strong> {risk.mitigation}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      )}\n\n      {activeTab === 'predictions' && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Attendance Prediction */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Target className=\"w-5 h-5 mr-2\" />\n                Attendance Prediction\n              </CardTitle>\n              <CardDescription>AI-powered attendance forecast</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center space-y-4\">\n                <div>\n                  <div className=\"text-3xl font-bold text-blue-600\">\n                    {insights.attendancePrediction.predicted}\n                  </div>\n                  <div className=\"text-gray-600\">Predicted Attendees</div>\n                </div>\n                <div className=\"flex items-center justify-center space-x-2\">\n                  <div className=\"text-sm text-gray-600\">Confidence:</div>\n                  <Badge variant=\"success\">\n                    {insights.attendancePrediction.confidence}%\n                  </Badge>\n                </div>\n                <div className=\"text-left\">\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">Key Factors:</h4>\n                  <ul className=\"text-sm text-gray-600 space-y-1\">\n                    {insights.attendancePrediction.factors.map((factor, index) => (\n                      <li key={index} className=\"flex items-center space-x-2\">\n                        <div className=\"w-1.5 h-1.5 bg-blue-600 rounded-full\" />\n                        <span>{factor}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Benchmark Comparison */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <TrendingUp className=\"w-5 h-5 mr-2\" />\n                Industry Benchmark\n              </CardTitle>\n              <CardDescription>How you compare to similar events</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm font-medium\">Your Performance</span>\n                  <span className=\"text-lg font-bold text-blue-600\">\n                    {analytics.attendanceRate.toFixed(1)}%\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm font-medium\">Industry Average</span>\n                  <span className=\"text-lg font-bold text-gray-600\">\n                    {insights.benchmarkComparison.industryAverage}%\n                  </span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                  <div\n                    className=\"bg-blue-600 h-3 rounded-full\"\n                    style={{ width: `${(analytics.attendanceRate / 100) * 100}%` }}\n                  />\n                </div>\n                <div className=\"text-center\">\n                  <Badge variant=\"success\">\n                    {insights.benchmarkComparison.percentile}th percentile\n                  </Badge>\n                  <p className=\"text-sm text-gray-600 mt-2\">\n                    You're performing better than {insights.benchmarkComparison.percentile}% of similar events\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;;;AAnCA;;;;;;;;;AA0CA,MAAM,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;CAAU;AAEvD,SAAS,mBAAmB,EAAE,OAAO,EAAE,UAAU,EAA2B;;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2C;IAEpF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG;QAAC;KAAQ;IAEZ,MAAM,gBAAgB;QACpB,IAAI;YACF,aAAa;YACb,MAAM,gBAAgB,MAAM,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE;YACnD,MAAM,eAAe,MAAM,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE;YAE9C,aAAa;YACb,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;gBAC5D,WAAU;;;;;;;;;;;IAIlB;IAEA,IAAI,CAAC,aAAa,CAAC,UAAU;QAC3B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;8BAC7B,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAe,WAAU;8BAAO;;;;;;;;;;;;IAKvD;IAEA,MAAM,aAAa;QACjB;YACE,OAAO;YACP,OAAO,UAAU,kBAAkB,CAAC,cAAc;YAClD,QAAQ;YACR,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,UAAU,cAAc,CAAC,cAAc;YAC9C,QAAQ,GAAG,UAAU,cAAc,CAAC,OAAO,CAAC,GAAG,iBAAiB,CAAC;YACjE,MAAM,8NAAA,CAAA,cAAW;YACjB,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,GAAG,UAAU,iBAAiB,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAClE,QAAQ;YACR,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,UAAU,iBAAiB,CAAC,kBAAkB;YACrD,QAAQ;YACR,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;;kCAEhC,6LAAC;wBAAI,WAAU;kCACZ,AAAC;4BAAC;4BAAY;4BAAY;yBAAc,CAAW,GAAG,CAAC,CAAC,oBACvD,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAS,cAAc,MAAM,YAAY;gCACzC,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CAET;+BALI;;;;;;;;;;;;;;;;0BAYb,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,QAAQ,sBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;kCAEhD,cAAA,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAqC,OAAO,KAAK;;;;;;8DAC9D,6LAAC;oDAAE,WAAU;8DAAoC,OAAO,KAAK;;;;;;8DAC7D,6LAAC;oDAAE,WAAU;8DAA8B,OAAO,MAAM;;;;;;;;;;;;sDAE1D,6LAAC;4CAAI,WAAW,CAAC,0BAA0B,EAAE,OAAO,KAAK,EAAE;sDACzD,cAAA,6LAAC,OAAO,IAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAd1B;;;;;;;;;;YAwBV,cAAc,4BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wCAAC,MAAM,UAAU,iBAAiB;;0DAC1C,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;0DACR,6LAAC,uJAAA,CAAA,OAAI;gDACH,MAAK;gDACL,SAAQ;gDACR,QAAO;gDACP,MAAK;gDACL,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wCAAC,MAAM,UAAU,YAAY;;0DACpC,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;0DACR,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAQ,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAgB;;0DACf,6LAAC;gDACC,MAAM,UAAU,eAAe,CAAC,SAAS;gDACzC,IAAG;gDACH,IAAG;gDACH,aAAa;gDACb,SAAQ;gDACR,SAAQ;0DAEP,UAAU,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC/C,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;uDAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;0DAG9B,6LAAC,0JAAA,CAAA,UAAO;;;;;0DACR,6LAAC,yJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOf,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGrC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,UAAU,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAClD,6LAAC;4CAA4B,WAAU;;8DACrC,6LAAC;oDAAK,WAAU;8DAAuB,SAAS,QAAQ;;;;;;8DACxD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,GAAG,AAAC,SAAS,KAAK,GAAG,KAAK,GAAG,IAAI,UAAU,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,KAAM,IAAI,CAAC,CAAC;gEAC1G;;;;;;;;;;;sEAGJ,6LAAC;4DAAK,WAAU;sEAA6B,SAAS,KAAK;;;;;;;;;;;;;2CAXrD,SAAS,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;YAqBtC,cAAc,4BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,SAAS,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,sBAClC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA+B,IAAI,KAAK;;;;;;8EACtD,6LAAC,oIAAA,CAAA,QAAK;oEACJ,SAAS,IAAI,QAAQ,KAAK,SAAS,gBAC5B,IAAI,QAAQ,KAAK,WAAW,YAAY;;wEAE9C,IAAI,QAAQ;wEAAC;;;;;;;;;;;;;sEAGlB,6LAAC;4DAAE,WAAU;sEAA8B,IAAI,WAAW;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;;gEAAqC;gEAC9B,IAAI,MAAM;;;;;;;;;;;;;;;;;;2CAnB7B;;;;;;;;;;;;;;;;;;;;;oBA8Bd,SAAS,WAAW,CAAC,MAAM,GAAG,mBAC7B,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG5C,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;4CAAgB,WAAU;sDACzB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAiC,KAAK,MAAM;;;;;;kFAC1D,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAS,KAAK,QAAQ,KAAK,SAAS,gBAAgB;;4EACxD,KAAK,QAAQ;4EAAC;;;;;;;;;;;;;0EAGnB,6LAAC;gEAAE,WAAU;;kFACX,6LAAC;kFAAO;;;;;;oEAAoB;oEAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;2CAX3C;;;;;;;;;;;;;;;;;;;;;;;;;;;YAwBvB,cAAc,+BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGrC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ,SAAS,oBAAoB,CAAC,SAAS;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;8DACvC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDACZ,SAAS,oBAAoB,CAAC,UAAU;wDAAC;;;;;;;;;;;;;sDAG9C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAG,WAAU;8DACX,SAAS,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAClD,6LAAC;4DAAe,WAAU;;8EACxB,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAM;;;;;;;2DAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYrB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGzC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;oDAAK,WAAU;;wDACb,UAAU,cAAc,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;oDAAK,WAAU;;wDACb,SAAS,mBAAmB,CAAC,eAAe;wDAAC;;;;;;;;;;;;;sDAGlD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,GAAG,AAAC,UAAU,cAAc,GAAG,MAAO,IAAI,CAAC,CAAC;gDAAC;;;;;;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDACZ,SAAS,mBAAmB,CAAC,UAAU;wDAAC;;;;;;;8DAE3C,6LAAC;oDAAE,WAAU;;wDAA6B;wDACT,SAAS,mBAAmB,CAAC,UAAU;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3F;GA3ZwB;KAAA", "debugId": null}}, {"offset": {"line": 3149, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/app/demo/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  Calendar, \n  QrCode, \n  BarChart3, \n  Users, \n  Play,\n  ArrowRight,\n  CheckCircle,\n  Smartphone,\n  Zap\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport QRScanner from '@/components/qr/qr-scanner'\nimport QRGenerator from '@/components/qr/qr-generator'\nimport AnalyticsDashboard from '@/components/analytics/analytics-dashboard'\nimport { TicketData } from '@/lib/qr-generator'\n\nconst demoSteps = [\n  {\n    id: 'create',\n    title: 'Create Event',\n    description: 'Set up your event with our intelligent form builder',\n    icon: Calendar,\n    color: 'from-blue-500 to-cyan-500',\n    features: ['AI-powered templates', 'Smart scheduling', 'Automated workflows']\n  },\n  {\n    id: 'register',\n    title: 'Registration & Ticketing',\n    description: 'Seamless registration with instant QR code generation',\n    icon: QrCode,\n    color: 'from-purple-500 to-pink-500',\n    features: ['QR code tickets', 'Mobile wallet integration', 'Automated confirmations']\n  },\n  {\n    id: 'checkin',\n    title: 'Smart Check-in',\n    description: 'Lightning-fast contactless check-in process',\n    icon: Smartphone,\n    color: 'from-green-500 to-emerald-500',\n    features: ['QR code scanning', 'Real-time verification', 'Instant updates']\n  },\n  {\n    id: 'analytics',\n    title: 'AI Analytics',\n    description: 'Real-time insights and predictive analytics',\n    icon: BarChart3,\n    color: 'from-orange-500 to-red-500',\n    features: ['Live dashboards', 'Attendance predictions', 'Automated reports']\n  }\n]\n\nconst mockTicketData: TicketData = {\n  registrationId: 'REG-2024-001',\n  eventId: 'EVT-2024-TECH',\n  attendeeId: 'ATT-2024-001',\n  eventTitle: 'Annual Tech Conference 2024',\n  attendeeName: 'John Smith',\n  attendeeEmail: '<EMAIL>',\n  timestamp: new Date().toISOString()\n}\n\nexport default function DemoPage() {\n  const [activeStep, setActiveStep] = useState<string>('create')\n  const [showQRScanner, setShowQRScanner] = useState(false)\n  const [showQRGenerator, setShowQRGenerator] = useState(false)\n  const [showAnalytics, setShowAnalytics] = useState(false)\n\n  const handleQRScanSuccess = (data: any) => {\n    console.log('QR Scan Success:', data)\n    setShowQRScanner(false)\n  }\n\n  const handleQRScanError = (error: string) => {\n    console.error('QR Scan Error:', error)\n  }\n\n  const renderStepContent = () => {\n    switch (activeStep) {\n      case 'create':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Intelligent Event Creation\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Our AI-powered form builder helps you create professional events in minutes, not hours.\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              {[\n                {\n                  title: 'Smart Templates',\n                  description: 'Choose from pre-built templates optimized for different event types',\n                  icon: '🎯'\n                },\n                {\n                  title: 'AI Suggestions',\n                  description: 'Get intelligent recommendations for timing, capacity, and content',\n                  icon: '🤖'\n                },\n                {\n                  title: 'Automated Setup',\n                  description: 'Registration forms, email templates, and workflows created automatically',\n                  icon: '⚡'\n                }\n              ].map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                >\n                  <Card className=\"text-center h-full\">\n                    <CardContent className=\"p-6\">\n                      <div className=\"text-3xl mb-4\">{feature.icon}</div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">{feature.title}</h4>\n                      <p className=\"text-gray-600 text-sm\">{feature.description}</p>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              ))}\n            </div>\n            \n            <div className=\"text-center\">\n              <Button variant=\"gradient\" size=\"lg\" onClick={() => window.open('/create-event', '_blank')}>\n                Try Event Creation\n                <ArrowRight className=\"ml-2 w-5 h-5\" />\n              </Button>\n            </div>\n          </div>\n        )\n\n      case 'register':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                QR Code Ticketing System\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Generate secure QR code tickets instantly with mobile wallet integration.\n              </p>\n            </div>\n\n            <div className=\"max-w-md mx-auto\">\n              {showQRGenerator ? (\n                <QRGenerator \n                  ticketData={mockTicketData}\n                  onClose={() => setShowQRGenerator(false)}\n                />\n              ) : (\n                <Card className=\"text-center\">\n                  <CardContent className=\"p-8\">\n                    <QrCode className=\"w-16 h-16 text-purple-600 mx-auto mb-4\" />\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      Generate Sample Ticket\n                    </h4>\n                    <p className=\"text-gray-600 mb-6\">\n                      See how our QR code ticketing system works with a live demo ticket.\n                    </p>\n                    <Button \n                      variant=\"gradient\" \n                      onClick={() => setShowQRGenerator(true)}\n                      className=\"w-full\"\n                    >\n                      Generate QR Ticket\n                    </Button>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n          </div>\n        )\n\n      case 'checkin':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Lightning-Fast Check-in\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Scan QR codes for instant verification and seamless attendee experience.\n              </p>\n            </div>\n\n            <div className=\"max-w-md mx-auto\">\n              <Card className=\"text-center\">\n                <CardContent className=\"p-8\">\n                  <Smartphone className=\"w-16 h-16 text-green-600 mx-auto mb-4\" />\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                    QR Code Scanner\n                  </h4>\n                  <p className=\"text-gray-600 mb-6\">\n                    Experience our mobile-optimized QR code scanner with real-time verification.\n                  </p>\n                  <Button \n                    variant=\"gradient\" \n                    onClick={() => setShowQRScanner(true)}\n                    className=\"w-full\"\n                  >\n                    <QrCode className=\"w-5 h-5 mr-2\" />\n                    Open Scanner Demo\n                  </Button>\n                </CardContent>\n              </Card>\n            </div>\n\n            {showQRScanner && (\n              <QRScanner\n                isOpen={showQRScanner}\n                onClose={() => setShowQRScanner(false)}\n                onScanSuccess={handleQRScanSuccess}\n                onScanError={handleQRScanError}\n              />\n            )}\n          </div>\n        )\n\n      case 'analytics':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                AI-Powered Analytics\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Get real-time insights, attendance predictions, and automated reports.\n              </p>\n            </div>\n\n            {showAnalytics ? (\n              <AnalyticsDashboard \n                eventId=\"demo-event\"\n                eventTitle=\"Annual Tech Conference 2024\"\n              />\n            ) : (\n              <div className=\"text-center\">\n                <Card className=\"max-w-md mx-auto\">\n                  <CardContent className=\"p-8\">\n                    <BarChart3 className=\"w-16 h-16 text-orange-600 mx-auto mb-4\" />\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      Live Analytics Dashboard\n                    </h4>\n                    <p className=\"text-gray-600 mb-6\">\n                      Explore our comprehensive analytics dashboard with real-time data and AI insights.\n                    </p>\n                    <Button \n                      variant=\"gradient\" \n                      onClick={() => setShowAnalytics(true)}\n                      className=\"w-full\"\n                    >\n                      View Analytics Demo\n                    </Button>\n                  </CardContent>\n                </Card>\n              </div>\n            )}\n          </div>\n        )\n\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <nav className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Calendar className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">EventFlow Pro</span>\n              <Badge variant=\"info\" className=\"ml-2\">Demo</Badge>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Button variant=\"outline\" onClick={() => window.open('/', '_blank')}>\n                Back to Home\n              </Button>\n              <Button variant=\"gradient\" onClick={() => window.open('/dashboard', '_blank')}>\n                View Dashboard\n              </Button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n              Experience EventFlow Pro\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Discover how our platform transforms event management from chaos to seamless execution.\n              Try each feature with interactive demos.\n            </p>\n          </motion.div>\n        </div>\n\n        {/* Step Navigation */}\n        <div className=\"mb-12\">\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {demoSteps.map((step, index) => (\n              <motion.button\n                key={step.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                onClick={() => setActiveStep(step.id)}\n                className={`flex items-center space-x-3 px-6 py-3 rounded-lg border transition-all ${\n                  activeStep === step.id\n                    ? 'bg-blue-50 border-blue-500 text-blue-700'\n                    : 'bg-white border-gray-200 text-gray-600 hover:border-gray-300'\n                }`}\n              >\n                <div className={`p-2 rounded-lg bg-gradient-to-r ${step.color}`}>\n                  <step.icon className=\"w-5 h-5 text-white\" />\n                </div>\n                <div className=\"text-left\">\n                  <div className=\"font-semibold\">{step.title}</div>\n                  <div className=\"text-sm opacity-75\">{step.description}</div>\n                </div>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n\n        {/* Step Content */}\n        <motion.div\n          key={activeStep}\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8\"\n        >\n          {renderStepContent()}\n        </motion.div>\n\n        {/* Call to Action */}\n        <div className=\"text-center mt-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white\"\n          >\n            <h2 className=\"text-2xl font-bold mb-4\">Ready to Transform Your Events?</h2>\n            <p className=\"text-blue-100 mb-6 max-w-2xl mx-auto\">\n              Join thousands of event organizers who've already made the switch from chaos to seamless execution.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" variant=\"secondary\">\n                Start Free Trial\n                <ArrowRight className=\"ml-2 w-5 h-5\" />\n              </Button>\n              <Button size=\"lg\" variant=\"outline\" className=\"border-white text-white hover:bg-white hover:text-gray-900\">\n                Schedule Demo\n              </Button>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;;;AApBA;;;;;;;;;;AAuBA,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;QACP,UAAU;YAAC;YAAwB;YAAoB;SAAsB;IAC/E;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,6MAAA,CAAA,SAAM;QACZ,OAAO;QACP,UAAU;YAAC;YAAmB;YAA6B;SAA0B;IACvF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,iNAAA,CAAA,aAAU;QAChB,OAAO;QACP,UAAU;YAAC;YAAoB;YAA0B;SAAkB;IAC7E;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qNAAA,CAAA,YAAS;QACf,OAAO;QACP,UAAU;YAAC;YAAmB;YAA0B;SAAoB;IAC9E;CACD;AAED,MAAM,iBAA6B;IACjC,gBAAgB;IAChB,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,eAAe;IACf,WAAW,IAAI,OAAO,WAAW;AACnC;AAEe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,oBAAoB;QAChC,iBAAiB;IACnB;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ,KAAK,CAAC,kBAAkB;IAClC;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAKpC,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;8CAEhD,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;8DAAiB,QAAQ,IAAI;;;;;;8DAC5C,6LAAC;oDAAG,WAAU;8DAAoC,QAAQ,KAAK;;;;;;8DAC/D,6LAAC;oDAAE,WAAU;8DAAyB,QAAQ,WAAW;;;;;;;;;;;;;;;;;mCATxD;;;;;;;;;;sCAgBX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAW,MAAK;gCAAK,SAAS,IAAM,OAAO,IAAI,CAAC,iBAAiB;;oCAAW;kDAE1F,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAMhC,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAKpC,6LAAC;4BAAI,WAAU;sCACZ,gCACC,6LAAC,8IAAA,CAAA,UAAW;gCACV,YAAY;gCACZ,SAAS,IAAM,mBAAmB;;;;;qDAGpC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,mBAAmB;4CAClC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUf,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAKpC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,iNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,iBAAiB;4CAChC,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;wBAO1C,+BACC,6LAAC,4IAAA,CAAA,UAAS;4BACR,QAAQ;4BACR,SAAS,IAAM,iBAAiB;4BAChC,eAAe;4BACf,aAAa;;;;;;;;;;;;YAMvB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;wBAKnC,8BACC,6LAAC,4JAAA,CAAA,UAAkB;4BACjB,SAAQ;4BACR,YAAW;;;;;iDAGb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUf;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;kDAClD,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAO,WAAU;kDAAO;;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK;kDAAW;;;;;;kDAGrE,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAW,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;;;;;;kCAQ3D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,SAAS,IAAM,cAAc,KAAK,EAAE;oCACpC,WAAW,CAAC,uEAAuE,EACjF,eAAe,KAAK,EAAE,GAClB,6CACA,gEACJ;;sDAEF,6LAAC;4CAAI,WAAW,CAAC,gCAAgC,EAAE,KAAK,KAAK,EAAE;sDAC7D,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiB,KAAK,KAAK;;;;;;8DAC1C,6LAAC;oDAAI,WAAU;8DAAsB,KAAK,WAAW;;;;;;;;;;;;;mCAhBlD,KAAK,EAAE;;;;;;;;;;;;;;;kCAwBpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET;uBANI;;;;;kCAUP,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,6LAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAGpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;;gDAAY;8DAEpC,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,WAAU;sDAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzH;GA3TwB;KAAA", "debugId": null}}]}