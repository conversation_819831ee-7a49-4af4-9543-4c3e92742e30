[{"name": "hot-reloader", "duration": 193, "timestamp": 7197660414, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1750395054408, "traceId": "c119574443be7bcc"}, {"name": "setup-dev-bundler", "duration": 873508, "timestamp": 7197341757, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750395054089, "traceId": "c119574443be7bcc"}, {"name": "run-instrumentation-hook", "duration": 38, "timestamp": 7198336541, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750395055084, "traceId": "c119574443be7bcc"}, {"name": "start-dev-server", "duration": 1753759, "timestamp": 7196614271, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "2665984000", "memory.totalMem": "16849256448", "memory.heapSizeLimit": "8474591232", "memory.rss": "185798656", "memory.heapTotal": "98222080", "memory.heapUsed": "74545640"}, "startTime": 1750395053362, "traceId": "c119574443be7bcc"}, {"name": "compile-path", "duration": 9174940, "timestamp": 7236999905, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750395093747, "traceId": "c119574443be7bcc"}, {"name": "ensure-page", "duration": 9180151, "timestamp": 7236997395, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750395093745, "traceId": "c119574443be7bcc"}]