'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  Calendar, 
  MapPin, 
  Users, 
  Clock, 
  Image as ImageIcon,
  Save,
  Eye,
  Wand2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'

const eventSchema = z.object({
  title: z.string().min(1, 'Event title is required').max(100, 'Title too long'),
  description: z.string().min(10, 'Description must be at least 10 characters').max(500, 'Description too long'),
  startDate: z.string().min(1, 'Start date is required'),
  startTime: z.string().min(1, 'Start time is required'),
  endDate: z.string().min(1, 'End date is required'),
  endTime: z.string().min(1, 'End time is required'),
  location: z.string().min(1, 'Location is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1').max(10000, 'Capacity too large'),
  registrationDeadline: z.string().optional(),
  eventType: z.enum(['conference', 'workshop', 'webinar', 'networking', 'training', 'other']),
  isPublic: z.boolean().default(true),
  requiresApproval: z.boolean().default(false),
})

type EventFormData = z.infer<typeof eventSchema>

interface EventFormProps {
  onSubmit: (data: EventFormData) => void
  onCancel: () => void
  initialData?: Partial<EventFormData>
  isEditing?: boolean
}

const eventTemplates = [
  {
    name: 'Tech Conference',
    type: 'conference' as const,
    description: 'Multi-day technology conference with keynotes and breakout sessions',
    defaultCapacity: 500,
    suggestedDuration: 8, // hours
  },
  {
    name: 'Workshop',
    type: 'workshop' as const,
    description: 'Interactive hands-on learning session',
    defaultCapacity: 50,
    suggestedDuration: 4,
  },
  {
    name: 'Webinar',
    type: 'webinar' as const,
    description: 'Online presentation or training session',
    defaultCapacity: 1000,
    suggestedDuration: 1.5,
  },
  {
    name: 'Networking Event',
    type: 'networking' as const,
    description: 'Professional networking and relationship building',
    defaultCapacity: 100,
    suggestedDuration: 3,
  }
]

export default function EventForm({ onSubmit, onCancel, initialData, isEditing = false }: EventFormProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [isGeneratingAI, setIsGeneratingAI] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset
  } = useForm<EventFormData>({
    resolver: zodResolver(eventSchema),
    defaultValues: {
      isPublic: true,
      requiresApproval: false,
      eventType: 'conference',
      ...initialData
    }
  })

  const watchedValues = watch()

  const applyTemplate = (template: typeof eventTemplates[0]) => {
    setSelectedTemplate(template.name)
    setValue('eventType', template.type)
    setValue('capacity', template.defaultCapacity)
    
    // Set suggested end time based on duration
    if (watchedValues.startDate && watchedValues.startTime) {
      const startDateTime = new Date(`${watchedValues.startDate}T${watchedValues.startTime}`)
      const endDateTime = new Date(startDateTime.getTime() + template.suggestedDuration * 60 * 60 * 1000)
      
      setValue('endDate', endDateTime.toISOString().split('T')[0])
      setValue('endTime', endDateTime.toTimeString().slice(0, 5))
    }
    
    toast.success(`Applied ${template.name} template`)
  }

  const generateAIContent = async () => {
    setIsGeneratingAI(true)
    
    // Simulate AI content generation
    setTimeout(() => {
      const aiSuggestions = {
        title: 'Annual Innovation Summit 2024',
        description: 'Join industry leaders and innovators for a day of cutting-edge insights, networking opportunities, and hands-on workshops. Discover the latest trends in technology, sustainability, and business transformation.',
        location: 'San Francisco Convention Center, Hall A'
      }
      
      setValue('title', aiSuggestions.title)
      setValue('description', aiSuggestions.description)
      setValue('location', aiSuggestions.location)
      
      setIsGeneratingAI(false)
      toast.success('AI suggestions applied!')
    }, 2000)
  }

  const onFormSubmit = (data: EventFormData) => {
    // Combine date and time fields
    const startDateTime = new Date(`${data.startDate}T${data.startTime}`)
    const endDateTime = new Date(`${data.endDate}T${data.endTime}`)
    
    if (endDateTime <= startDateTime) {
      toast.error('End date/time must be after start date/time')
      return
    }
    
    onSubmit(data)
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {isEditing ? 'Edit Event' : 'Create New Event'}
        </h1>
        <p className="text-gray-600">
          {isEditing ? 'Update your event details' : 'Set up your event with our intelligent form'}
        </p>
      </div>

      {/* Templates */}
      {!isEditing && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Wand2 className="w-5 h-5 mr-2" />
              Quick Start Templates
            </CardTitle>
            <CardDescription>Choose a template to get started quickly</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {eventTemplates.map((template) => (
                <motion.div
                  key={template.name}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedTemplate === template.name
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => applyTemplate(template)}
                >
                  <h3 className="font-semibold text-gray-900 mb-1">{template.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{template.defaultCapacity} people</span>
                    <span>{template.suggestedDuration}h</span>
                  </div>
                </motion.div>
              ))}
            </div>
            <div className="mt-4 text-center">
              <Button
                type="button"
                variant="outline"
                onClick={generateAIContent}
                disabled={isGeneratingAI}
              >
                {isGeneratingAI ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="mr-2"
                    >
                      <Wand2 className="w-4 h-4" />
                    </motion.div>
                    Generating AI Suggestions...
                  </>
                ) : (
                  <>
                    <Wand2 className="w-4 h-4 mr-2" />
                    Generate with AI
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>Essential details about your event</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Event Title *
              </label>
              <Input
                {...register('title')}
                placeholder="Enter event title"
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && (
                <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                {...register('description')}
                rows={4}
                placeholder="Describe your event..."
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Event Type *
                </label>
                <select
                  {...register('eventType')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="conference">Conference</option>
                  <option value="workshop">Workshop</option>
                  <option value="webinar">Webinar</option>
                  <option value="networking">Networking</option>
                  <option value="training">Training</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Capacity *
                </label>
                <Input
                  {...register('capacity', { valueAsNumber: true })}
                  type="number"
                  placeholder="Maximum attendees"
                  className={errors.capacity ? 'border-red-500' : ''}
                />
                {errors.capacity && (
                  <p className="text-red-500 text-sm mt-1">{errors.capacity.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Date & Time */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Date & Time
            </CardTitle>
            <CardDescription>When will your event take place?</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date *
                </label>
                <Input
                  {...register('startDate')}
                  type="date"
                  className={errors.startDate ? 'border-red-500' : ''}
                />
                {errors.startDate && (
                  <p className="text-red-500 text-sm mt-1">{errors.startDate.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Time *
                </label>
                <Input
                  {...register('startTime')}
                  type="time"
                  className={errors.startTime ? 'border-red-500' : ''}
                />
                {errors.startTime && (
                  <p className="text-red-500 text-sm mt-1">{errors.startTime.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Date *
                </label>
                <Input
                  {...register('endDate')}
                  type="date"
                  className={errors.endDate ? 'border-red-500' : ''}
                />
                {errors.endDate && (
                  <p className="text-red-500 text-sm mt-1">{errors.endDate.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Time *
                </label>
                <Input
                  {...register('endTime')}
                  type="time"
                  className={errors.endTime ? 'border-red-500' : ''}
                />
                {errors.endTime && (
                  <p className="text-red-500 text-sm mt-1">{errors.endTime.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Registration Deadline
              </label>
              <Input
                {...register('registrationDeadline')}
                type="datetime-local"
              />
            </div>
          </CardContent>
        </Card>

        {/* Location */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MapPin className="w-5 h-5 mr-2" />
              Location
            </CardTitle>
            <CardDescription>Where will your event be held?</CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Venue/Location *
              </label>
              <Input
                {...register('location')}
                placeholder="Enter venue name and address"
                className={errors.location ? 'border-red-500' : ''}
              />
              {errors.location && (
                <p className="text-red-500 text-sm mt-1">{errors.location.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Event Settings</CardTitle>
            <CardDescription>Configure registration and visibility options</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <input
                {...register('isPublic')}
                type="checkbox"
                id="isPublic"
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="isPublic" className="text-sm font-medium text-gray-700">
                Make this event public
              </label>
            </div>

            <div className="flex items-center space-x-3">
              <input
                {...register('requiresApproval')}
                type="checkbox"
                id="requiresApproval"
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="requiresApproval" className="text-sm font-medium text-gray-700">
                Require approval for registrations
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="button" variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button type="submit" variant="gradient" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="mr-2"
                >
                  <Save className="w-4 h-4" />
                </motion.div>
                {isEditing ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                {isEditing ? 'Update Event' : 'Create Event'}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
