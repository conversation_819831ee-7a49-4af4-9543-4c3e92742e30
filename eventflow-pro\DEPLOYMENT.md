# 🚀 EventFlow Pro - Deployment Guide

## 🌐 Quick Deploy Options

### **Option 1: Vercel (Recommended)**
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/eventflow-pro)

### **Option 2: Netlify**
[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/your-username/eventflow-pro)

### **Option 3: Railway**
[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template?template=https://github.com/your-username/eventflow-pro)

## 📋 Prerequisites

- Node.js 18+ installed
- Git installed
- Supabase account (free tier available)
- Domain name (optional, for custom domains)

## 🔧 Environment Setup

### **1. Supabase Configuration**

1. **Create a Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Click "Start your project"
   - Create a new organization and project

2. **Get Your Credentials**
   ```bash
   # From Supabase Dashboard > Settings > API
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

3. **Set Up Database Schema**
   ```sql
   -- Run in Supabase SQL Editor
   
   -- Profiles table
   CREATE TABLE profiles (
     id UUID REFERENCES auth.users ON DELETE CASCADE,
     email TEXT UNIQUE NOT NULL,
     full_name TEXT,
     avatar_url TEXT,
     role TEXT DEFAULT 'attendee' CHECK (role IN ('organizer', 'attendee', 'admin')),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     PRIMARY KEY (id)
   );

   -- Events table
   CREATE TABLE events (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     title TEXT NOT NULL,
     description TEXT,
     start_date TIMESTAMP WITH TIME ZONE NOT NULL,
     end_date TIMESTAMP WITH TIME ZONE NOT NULL,
     location TEXT,
     venue_capacity INTEGER,
     organizer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
     status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'cancelled', 'completed')),
     registration_open BOOLEAN DEFAULT true,
     registration_deadline TIMESTAMP WITH TIME ZONE,
     cover_image_url TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Registrations table
   CREATE TABLE registrations (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     event_id UUID REFERENCES events(id) ON DELETE CASCADE,
     attendee_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
     status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'checked_in')),
     registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     check_in_time TIMESTAMP WITH TIME ZONE,
     qr_code TEXT UNIQUE NOT NULL,
     ticket_type TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     UNIQUE(event_id, attendee_id)
   );

   -- Analytics table
   CREATE TABLE analytics (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     event_id UUID REFERENCES events(id) ON DELETE CASCADE,
     metric_type TEXT NOT NULL,
     metric_value NUMERIC NOT NULL,
     timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     metadata JSONB
   );

   -- Enable Row Level Security
   ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
   ALTER TABLE events ENABLE ROW LEVEL SECURITY;
   ALTER TABLE registrations ENABLE ROW LEVEL SECURITY;
   ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;

   -- Create policies (basic examples)
   CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
   CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
   ```

### **2. Environment Variables**

Create `.env.local` file:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME=EventFlow Pro

# Optional: Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Optional: AI Analytics
OPENAI_API_KEY=your-openai-api-key

# Optional: Payment Processing
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-key
STRIPE_SECRET_KEY=your-stripe-secret
STRIPE_WEBHOOK_SECRET=your-webhook-secret
```

## 🚀 Deployment Steps

### **Vercel Deployment**

1. **Connect Repository**
   ```bash
   # Push to GitHub
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Configure environment variables
   - Deploy!

3. **Configure Environment Variables**
   - In Vercel dashboard, go to Settings > Environment Variables
   - Add all variables from your `.env.local`
   - Redeploy the project

### **Netlify Deployment**

1. **Build Configuration**
   Create `netlify.toml`:
   ```toml
   [build]
     command = "npm run build"
     publish = "out"

   [build.environment]
     NODE_VERSION = "18"

   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200
   ```

2. **Deploy**
   - Connect GitHub repository to Netlify
   - Set build command: `npm run build`
   - Set publish directory: `out`
   - Add environment variables
   - Deploy!

### **Self-Hosted Deployment**

1. **Server Setup**
   ```bash
   # Clone repository
   git clone https://github.com/your-username/eventflow-pro.git
   cd eventflow-pro

   # Install dependencies
   npm install

   # Build application
   npm run build

   # Start production server
   npm start
   ```

2. **Process Manager (PM2)**
   ```bash
   # Install PM2
   npm install -g pm2

   # Start application
   pm2 start npm --name "eventflow-pro" -- start

   # Save PM2 configuration
   pm2 save
   pm2 startup
   ```

3. **Nginx Configuration**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## 🐳 Docker Deployment

### **Dockerfile**
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### **Docker Compose**
```yaml
version: '3.8'

services:
  eventflow-pro:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - eventflow-pro
    restart: unless-stopped
```

## 🔒 Security Considerations

### **Environment Variables**
- Never commit `.env.local` to version control
- Use different keys for development and production
- Rotate API keys regularly

### **Database Security**
- Enable Row Level Security (RLS) in Supabase
- Create appropriate policies for data access
- Use service role key only for server-side operations

### **HTTPS Configuration**
- Always use HTTPS in production
- Configure SSL certificates (Let's Encrypt recommended)
- Set up proper security headers

## 📊 Monitoring & Analytics

### **Performance Monitoring**
- Set up Vercel Analytics
- Configure Google Analytics
- Monitor Core Web Vitals

### **Error Tracking**
- Integrate Sentry for error tracking
- Set up logging for debugging
- Monitor API response times

### **Uptime Monitoring**
- Use services like Pingdom or UptimeRobot
- Set up alerts for downtime
- Monitor database performance

## 🔄 CI/CD Pipeline

### **GitHub Actions**
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - run: npm run test
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 🎯 Post-Deployment Checklist

- [ ] Verify all environment variables are set
- [ ] Test user registration and login
- [ ] Verify QR code generation and scanning
- [ ] Check analytics dashboard functionality
- [ ] Test mobile responsiveness
- [ ] Verify email notifications work
- [ ] Set up monitoring and alerts
- [ ] Configure custom domain (if applicable)
- [ ] Set up SSL certificate
- [ ] Test performance with Lighthouse

## 🆘 Troubleshooting

### **Common Issues**

1. **Build Failures**
   - Check Node.js version (18+ required)
   - Verify all dependencies are installed
   - Check for TypeScript errors

2. **Database Connection Issues**
   - Verify Supabase URL and keys
   - Check database schema is set up
   - Ensure RLS policies are configured

3. **Environment Variable Issues**
   - Ensure all required variables are set
   - Check variable names match exactly
   - Verify values are correct

### **Getting Help**
- Check the [GitHub Issues](https://github.com/your-username/eventflow-pro/issues)
- Join our [Discord community](https://discord.gg/eventflowpro)
- Email support: <EMAIL>

---

**🎉 Congratulations! Your EventFlow Pro platform is now live and ready to transform event management!**
