'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Calendar, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import EventForm from '@/components/event/event-form'
import { toast } from 'sonner'

type EventFormData = {
  title: string
  description: string
  startDate: string
  startTime: string
  endDate: string
  endTime: string
  location: string
  capacity: number
  registrationDeadline?: string
  eventType: 'conference' | 'workshop' | 'webinar' | 'networking' | 'training' | 'other'
  isPublic: boolean
  requiresApproval: boolean
}

export default function CreateEventPage() {
  const router = useRouter()
  const [isCreating, setIsCreating] = useState(false)

  const handleSubmit = async (data: EventFormData) => {
    setIsCreating(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In a real app, you would send this to your API
      console.log('Creating event:', data)
      
      toast.success('Event created successfully!')
      
      // Redirect to dashboard or event page
      router.push('/dashboard')
    } catch (error) {
      console.error('Failed to create event:', error)
      toast.error('Failed to create event. Please try again.')
    } finally {
      setIsCreating(false)
    }
  }

  const handleCancel = () => {
    router.push('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => router.push('/dashboard')}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Dashboard</span>
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">EventFlow Pro</span>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <EventForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </motion.div>
      </div>
    </div>
  )
}
