'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Calendar, 
  QrCode, 
  BarChart3, 
  Users, 
  Play,
  ArrowRight,
  CheckCircle,
  Smartphone,
  Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import QRScanner from '@/components/qr/qr-scanner'
import QRGenerator from '@/components/qr/qr-generator'
import AnalyticsDashboard from '@/components/analytics/analytics-dashboard'
import { TicketData } from '@/lib/qr-generator'

const demoSteps = [
  {
    id: 'create',
    title: 'Create Event',
    description: 'Set up your event with our intelligent form builder',
    icon: Calendar,
    color: 'from-blue-500 to-cyan-500',
    features: ['AI-powered templates', 'Smart scheduling', 'Automated workflows']
  },
  {
    id: 'register',
    title: 'Registration & Ticketing',
    description: 'Seamless registration with instant QR code generation',
    icon: QrCode,
    color: 'from-purple-500 to-pink-500',
    features: ['QR code tickets', 'Mobile wallet integration', 'Automated confirmations']
  },
  {
    id: 'checkin',
    title: 'Smart Check-in',
    description: 'Lightning-fast contactless check-in process',
    icon: Smartphone,
    color: 'from-green-500 to-emerald-500',
    features: ['QR code scanning', 'Real-time verification', 'Instant updates']
  },
  {
    id: 'analytics',
    title: 'AI Analytics',
    description: 'Real-time insights and predictive analytics',
    icon: BarChart3,
    color: 'from-orange-500 to-red-500',
    features: ['Live dashboards', 'Attendance predictions', 'Automated reports']
  }
]

const mockTicketData: TicketData = {
  registrationId: 'REG-2024-001',
  eventId: 'EVT-2024-TECH',
  attendeeId: 'ATT-2024-001',
  eventTitle: 'Annual Tech Conference 2024',
  attendeeName: 'John Smith',
  attendeeEmail: '<EMAIL>',
  timestamp: new Date().toISOString()
}

export default function DemoPage() {
  const [activeStep, setActiveStep] = useState<string>('create')
  const [showQRScanner, setShowQRScanner] = useState(false)
  const [showQRGenerator, setShowQRGenerator] = useState(false)
  const [showAnalytics, setShowAnalytics] = useState(false)

  const handleQRScanSuccess = (data: any) => {
    console.log('QR Scan Success:', data)
    setShowQRScanner(false)
  }

  const handleQRScanError = (error: string) => {
    console.error('QR Scan Error:', error)
  }

  const renderStepContent = () => {
    switch (activeStep) {
      case 'create':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Intelligent Event Creation
              </h3>
              <p className="text-gray-600 mb-6">
                Our AI-powered form builder helps you create professional events in minutes, not hours.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  title: 'Smart Templates',
                  description: 'Choose from pre-built templates optimized for different event types',
                  icon: '🎯'
                },
                {
                  title: 'AI Suggestions',
                  description: 'Get intelligent recommendations for timing, capacity, and content',
                  icon: '🤖'
                },
                {
                  title: 'Automated Setup',
                  description: 'Registration forms, email templates, and workflows created automatically',
                  icon: '⚡'
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="text-center h-full">
                    <CardContent className="p-6">
                      <div className="text-3xl mb-4">{feature.icon}</div>
                      <h4 className="font-semibold text-gray-900 mb-2">{feature.title}</h4>
                      <p className="text-gray-600 text-sm">{feature.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
            
            <div className="text-center">
              <Button variant="gradient" size="lg" onClick={() => window.open('/create-event', '_blank')}>
                Try Event Creation
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </div>
          </div>
        )

      case 'register':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                QR Code Ticketing System
              </h3>
              <p className="text-gray-600 mb-6">
                Generate secure QR code tickets instantly with mobile wallet integration.
              </p>
            </div>

            <div className="max-w-md mx-auto">
              {showQRGenerator ? (
                <QRGenerator 
                  ticketData={mockTicketData}
                  onClose={() => setShowQRGenerator(false)}
                />
              ) : (
                <Card className="text-center">
                  <CardContent className="p-8">
                    <QrCode className="w-16 h-16 text-purple-600 mx-auto mb-4" />
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      Generate Sample Ticket
                    </h4>
                    <p className="text-gray-600 mb-6">
                      See how our QR code ticketing system works with a live demo ticket.
                    </p>
                    <Button 
                      variant="gradient" 
                      onClick={() => setShowQRGenerator(true)}
                      className="w-full"
                    >
                      Generate QR Ticket
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )

      case 'checkin':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Lightning-Fast Check-in
              </h3>
              <p className="text-gray-600 mb-6">
                Scan QR codes for instant verification and seamless attendee experience.
              </p>
            </div>

            <div className="max-w-md mx-auto">
              <Card className="text-center">
                <CardContent className="p-8">
                  <Smartphone className="w-16 h-16 text-green-600 mx-auto mb-4" />
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    QR Code Scanner
                  </h4>
                  <p className="text-gray-600 mb-6">
                    Experience our mobile-optimized QR code scanner with real-time verification.
                  </p>
                  <Button 
                    variant="gradient" 
                    onClick={() => setShowQRScanner(true)}
                    className="w-full"
                  >
                    <QrCode className="w-5 h-5 mr-2" />
                    Open Scanner Demo
                  </Button>
                </CardContent>
              </Card>
            </div>

            {showQRScanner && (
              <QRScanner
                isOpen={showQRScanner}
                onClose={() => setShowQRScanner(false)}
                onScanSuccess={handleQRScanSuccess}
                onScanError={handleQRScanError}
              />
            )}
          </div>
        )

      case 'analytics':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                AI-Powered Analytics
              </h3>
              <p className="text-gray-600 mb-6">
                Get real-time insights, attendance predictions, and automated reports.
              </p>
            </div>

            {showAnalytics ? (
              <AnalyticsDashboard 
                eventId="demo-event"
                eventTitle="Annual Tech Conference 2024"
              />
            ) : (
              <div className="text-center">
                <Card className="max-w-md mx-auto">
                  <CardContent className="p-8">
                    <BarChart3 className="w-16 h-16 text-orange-600 mx-auto mb-4" />
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      Live Analytics Dashboard
                    </h4>
                    <p className="text-gray-600 mb-6">
                      Explore our comprehensive analytics dashboard with real-time data and AI insights.
                    </p>
                    <Button 
                      variant="gradient" 
                      onClick={() => setShowAnalytics(true)}
                      className="w-full"
                    >
                      View Analytics Demo
                    </Button>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">EventFlow Pro</span>
              <Badge variant="info" className="ml-2">Demo</Badge>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" onClick={() => window.open('/', '_blank')}>
                Back to Home
              </Button>
              <Button variant="gradient" onClick={() => window.open('/dashboard', '_blank')}>
                View Dashboard
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Experience EventFlow Pro
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover how our platform transforms event management from chaos to seamless execution.
              Try each feature with interactive demos.
            </p>
          </motion.div>
        </div>

        {/* Step Navigation */}
        <div className="mb-12">
          <div className="flex flex-wrap justify-center gap-4">
            {demoSteps.map((step, index) => (
              <motion.button
                key={step.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                onClick={() => setActiveStep(step.id)}
                className={`flex items-center space-x-3 px-6 py-3 rounded-lg border transition-all ${
                  activeStep === step.id
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'bg-white border-gray-200 text-gray-600 hover:border-gray-300'
                }`}
              >
                <div className={`p-2 rounded-lg bg-gradient-to-r ${step.color}`}>
                  <step.icon className="w-5 h-5 text-white" />
                </div>
                <div className="text-left">
                  <div className="font-semibold">{step.title}</div>
                  <div className="text-sm opacity-75">{step.description}</div>
                </div>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <motion.div
          key={activeStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
        >
          {renderStepContent()}
        </motion.div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white"
          >
            <h2 className="text-2xl font-bold mb-4">Ready to Transform Your Events?</h2>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Join thousands of event organizers who've already made the switch from chaos to seamless execution.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary">
                Start Free Trial
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-gray-900">
                Schedule Demo
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
