import { createBrowserClient } from '@supabase/ssr'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const createClient = () => {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Server-side Supabase client
export const createServerSupabaseClient = async () => {
  const cookieStore = await cookies()

  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll()
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set(name, value, options)
          )
        } catch {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    },
  })
}

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'organizer' | 'attendee' | 'admin'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'organizer' | 'attendee' | 'admin'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'organizer' | 'attendee' | 'admin'
          created_at?: string
          updated_at?: string
        }
      }
      events: {
        Row: {
          id: string
          title: string
          description: string | null
          start_date: string
          end_date: string
          location: string | null
          venue_capacity: number | null
          organizer_id: string
          status: 'draft' | 'published' | 'cancelled' | 'completed'
          registration_open: boolean
          registration_deadline: string | null
          cover_image_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          start_date: string
          end_date: string
          location?: string | null
          venue_capacity?: number | null
          organizer_id: string
          status?: 'draft' | 'published' | 'cancelled' | 'completed'
          registration_open?: boolean
          registration_deadline?: string | null
          cover_image_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          start_date?: string
          end_date?: string
          location?: string | null
          venue_capacity?: number | null
          organizer_id?: string
          status?: 'draft' | 'published' | 'cancelled' | 'completed'
          registration_open?: boolean
          registration_deadline?: string | null
          cover_image_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      registrations: {
        Row: {
          id: string
          event_id: string
          attendee_id: string
          status: 'pending' | 'confirmed' | 'cancelled' | 'checked_in'
          registration_date: string
          check_in_time: string | null
          qr_code: string
          ticket_type: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          event_id: string
          attendee_id: string
          status?: 'pending' | 'confirmed' | 'cancelled' | 'checked_in'
          registration_date?: string
          check_in_time?: string | null
          qr_code: string
          ticket_type?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          event_id?: string
          attendee_id?: string
          status?: 'pending' | 'confirmed' | 'cancelled' | 'checked_in'
          registration_date?: string
          check_in_time?: string | null
          qr_code?: string
          ticket_type?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      analytics: {
        Row: {
          id: string
          event_id: string
          metric_type: string
          metric_value: number
          timestamp: string
          metadata: any | null
        }
        Insert: {
          id?: string
          event_id: string
          metric_type: string
          metric_value: number
          timestamp?: string
          metadata?: any | null
        }
        Update: {
          id?: string
          event_id?: string
          metric_type?: string
          metric_value?: number
          timestamp?: string
          metadata?: any | null
        }
      }
    }
  }
}
