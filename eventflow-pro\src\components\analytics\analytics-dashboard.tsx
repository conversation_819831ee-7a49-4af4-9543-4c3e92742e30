'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  Users, 
  Clock, 
  Target,
  Brain,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart as RechartsPieChart, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend
} from 'recharts'
import { generateEventAnalytics, generateAIInsights, EventAnalytics, AIInsights } from '@/lib/ai-analytics'

interface AnalyticsDashboardProps {
  eventId: string
  eventTitle: string
}

const COLORS = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444']

export default function AnalyticsDashboard({ eventId, eventTitle }: AnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<EventAnalytics | null>(null)
  const [insights, setInsights] = useState<AIInsights | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'insights' | 'predictions'>('overview')

  useEffect(() => {
    loadAnalytics()
  }, [eventId])

  const loadAnalytics = async () => {
    try {
      setIsLoading(true)
      const analyticsData = await generateEventAnalytics(eventId)
      const insightsData = await generateAIInsights(analyticsData)
      
      setAnalytics(analyticsData)
      setInsights(insightsData)
    } catch (error) {
      console.error('Failed to load analytics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full"
        />
      </div>
    )
  }

  if (!analytics || !insights) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">Failed to load analytics data</p>
        <Button onClick={loadAnalytics} className="mt-4">
          Try Again
        </Button>
      </div>
    )
  }

  const keyMetrics = [
    {
      title: 'Total Registrations',
      value: analytics.totalRegistrations.toLocaleString(),
      change: '+12% vs last event',
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: 'Checked In',
      value: analytics.checkedInCount.toLocaleString(),
      change: `${analytics.attendanceRate.toFixed(1)}% attendance rate`,
      icon: CheckCircle,
      color: 'text-green-600'
    },
    {
      title: 'Avg. Session Duration',
      value: `${analytics.engagementMetrics.averageSessionDuration} min`,
      change: '+8% vs industry avg',
      icon: Clock,
      color: 'text-purple-600'
    },
    {
      title: 'Peak Attendance',
      value: analytics.engagementMetrics.peakAttendanceTime,
      change: 'Optimal timing',
      icon: TrendingUp,
      color: 'text-orange-600'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
          <p className="text-gray-600">{eventTitle}</p>
        </div>
        <div className="flex space-x-2">
          {(['overview', 'insights', 'predictions'] as const).map((tab) => (
            <Button
              key={tab}
              variant={activeTab === tab ? 'default' : 'outline'}
              onClick={() => setActiveTab(tab)}
              className="capitalize"
            >
              {tab}
            </Button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {keyMetrics.map((metric, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                    <p className="text-sm text-gray-500 mt-1">{metric.change}</p>
                  </div>
                  <div className={`p-3 rounded-lg bg-gray-50 ${metric.color}`}>
                    <metric.icon className="w-6 h-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Registration Trend */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Registration Trend
              </CardTitle>
              <CardDescription>Daily registration count over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={analytics.registrationTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Area 
                    type="monotone" 
                    dataKey="count" 
                    stroke="#3B82F6" 
                    fill="#3B82F6" 
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Check-in Pattern */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                Check-in Pattern
              </CardTitle>
              <CardDescription>Hourly check-in distribution</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analytics.checkInTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#10B981" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Demographics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="w-5 h-5 mr-2" />
                Age Demographics
              </CardTitle>
              <CardDescription>Attendee age distribution</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={analytics.demographicData.ageGroups}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="count"
                    nameKey="group"
                  >
                    {analytics.demographicData.ageGroups.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Geographic Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Target className="w-5 h-5 mr-2" />
                Geographic Distribution
              </CardTitle>
              <CardDescription>Attendee locations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analytics.demographicData.locations.map((location, index) => (
                  <div key={location.location} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{location.location}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{
                            width: `${(location.count / Math.max(...analytics.demographicData.locations.map(l => l.count))) * 100}%`
                          }}
                        />
                      </div>
                      <span className="text-sm text-gray-600 w-8">{location.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'insights' && (
        <div className="space-y-6">
          {/* AI Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Brain className="w-5 h-5 mr-2" />
                AI-Powered Recommendations
              </CardTitle>
              <CardDescription>Actionable insights to improve your event</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {insights.recommendations.map((rec, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-semibold text-gray-900">{rec.title}</h4>
                          <Badge 
                            variant={rec.priority === 'high' ? 'destructive' : 
                                   rec.priority === 'medium' ? 'warning' : 'secondary'}
                          >
                            {rec.priority} priority
                          </Badge>
                        </div>
                        <p className="text-gray-600 text-sm mb-2">{rec.description}</p>
                        <p className="text-green-600 text-sm font-medium">
                          Expected Impact: {rec.impact}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Risk Factors */}
          {insights.riskFactors.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  Risk Assessment
                </CardTitle>
                <CardDescription>Potential issues and mitigation strategies</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {insights.riskFactors.map((risk, index) => (
                    <div key={index} className="border border-orange-200 bg-orange-50 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5" />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-semibold text-orange-900">{risk.factor}</h4>
                            <Badge variant={risk.severity === 'high' ? 'destructive' : 'warning'}>
                              {risk.severity} risk
                            </Badge>
                          </div>
                          <p className="text-orange-800 text-sm">
                            <strong>Mitigation:</strong> {risk.mitigation}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {activeTab === 'predictions' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Attendance Prediction */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Target className="w-5 h-5 mr-2" />
                Attendance Prediction
              </CardTitle>
              <CardDescription>AI-powered attendance forecast</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center space-y-4">
                <div>
                  <div className="text-3xl font-bold text-blue-600">
                    {insights.attendancePrediction.predicted}
                  </div>
                  <div className="text-gray-600">Predicted Attendees</div>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <div className="text-sm text-gray-600">Confidence:</div>
                  <Badge variant="success">
                    {insights.attendancePrediction.confidence}%
                  </Badge>
                </div>
                <div className="text-left">
                  <h4 className="font-semibold text-gray-900 mb-2">Key Factors:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {insights.attendancePrediction.factors.map((factor, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full" />
                        <span>{factor}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Benchmark Comparison */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Industry Benchmark
              </CardTitle>
              <CardDescription>How you compare to similar events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Your Performance</span>
                  <span className="text-lg font-bold text-blue-600">
                    {analytics.attendanceRate.toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Industry Average</span>
                  <span className="text-lg font-bold text-gray-600">
                    {insights.benchmarkComparison.industryAverage}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-blue-600 h-3 rounded-full"
                    style={{ width: `${(analytics.attendanceRate / 100) * 100}%` }}
                  />
                </div>
                <div className="text-center">
                  <Badge variant="success">
                    {insights.benchmarkComparison.percentile}th percentile
                  </Badge>
                  <p className="text-sm text-gray-600 mt-2">
                    You're performing better than {insights.benchmarkComparison.percentile}% of similar events
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
