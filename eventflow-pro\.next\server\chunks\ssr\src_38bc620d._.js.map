{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatDateShort(date: string | Date) {\n  return new Intl.DateTimeFormat('en-US', {\n    month: 'short',\n    day: 'numeric',\n    year: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function generateId() {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n\nexport function truncateText(text: string, maxLength: number) {\n  if (text.length <= maxLength) return text\n  return text.substring(0, maxLength) + '...'\n}\n\nexport function calculateEventDuration(startDate: string, endDate: string) {\n  const start = new Date(startDate)\n  const end = new Date(endDate)\n  const diffMs = end.getTime() - start.getTime()\n  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))\n  const diffDays = Math.floor(diffHours / 24)\n  \n  if (diffDays > 0) {\n    return `${diffDays} day${diffDays > 1 ? 's' : ''}`\n  } else {\n    return `${diffHours} hour${diffHours > 1 ? 's' : ''}`\n  }\n}\n\nexport function getEventStatus(event: {\n  start_date: string\n  end_date: string\n  status: string\n}) {\n  const now = new Date()\n  const startDate = new Date(event.start_date)\n  const endDate = new Date(event.end_date)\n  \n  if (event.status === 'cancelled') return 'cancelled'\n  if (event.status === 'draft') return 'draft'\n  if (now < startDate) return 'upcoming'\n  if (now >= startDate && now <= endDate) return 'live'\n  if (now > endDate) return 'completed'\n  \n  return event.status\n}\n\nexport function getStatusColor(status: string) {\n  switch (status) {\n    case 'draft':\n      return 'bg-gray-100 text-gray-800'\n    case 'upcoming':\n      return 'bg-blue-100 text-blue-800'\n    case 'live':\n      return 'bg-green-100 text-green-800'\n    case 'completed':\n      return 'bg-purple-100 text-purple-800'\n    case 'cancelled':\n      return 'bg-red-100 text-red-800'\n    default:\n      return 'bg-gray-100 text-gray-800'\n  }\n}\n\nexport function validateEmail(email: string) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,gBAAgB,IAAmB;IACjD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,OAAO;QACP,KAAK;QACL,MAAM;IACR,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,uBAAuB,SAAiB,EAAE,OAAe;IACvE,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,SAAS,IAAI,OAAO,KAAK,MAAM,OAAO;IAC5C,MAAM,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,EAAE;IACrD,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;IAExC,IAAI,WAAW,GAAG;QAChB,OAAO,GAAG,SAAS,IAAI,EAAE,WAAW,IAAI,MAAM,IAAI;IACpD,OAAO;QACL,OAAO,GAAG,UAAU,KAAK,EAAE,YAAY,IAAI,MAAM,IAAI;IACvD;AACF;AAEO,SAAS,eAAe,KAI9B;IACC,MAAM,MAAM,IAAI;IAChB,MAAM,YAAY,IAAI,KAAK,MAAM,UAAU;IAC3C,MAAM,UAAU,IAAI,KAAK,MAAM,QAAQ;IAEvC,IAAI,MAAM,MAAM,KAAK,aAAa,OAAO;IACzC,IAAI,MAAM,MAAM,KAAK,SAAS,OAAO;IACrC,IAAI,MAAM,WAAW,OAAO;IAC5B,IAAI,OAAO,aAAa,OAAO,SAAS,OAAO;IAC/C,IAAI,MAAM,SAAS,OAAO;IAE1B,OAAO,MAAM,MAAM;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        gradient: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/D%20Drive/Projects/Event%20Management/eventflow-pro/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport {\n  Calendar,\n  QrCode,\n  BarChart3,\n  Users,\n  Clock,\n  CheckCircle,\n  Smartphone,\n  Zap,\n  TrendingUp,\n  Shield,\n  ArrowRight,\n  Play\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\n\nexport default function Home() {\n  const [activeFeature, setActiveFeature] = useState(0)\n\n  const features = [\n    {\n      icon: Calendar,\n      title: \"Smart Event Creation\",\n      description: \"Drag-and-drop event builder with intelligent templates and automated scheduling suggestions.\",\n      color: \"from-blue-500 to-cyan-500\"\n    },\n    {\n      icon: QrCode,\n      title: \"QR Code Ticketing\",\n      description: \"Instant ticket generation with secure QR codes, mobile wallet integration, and contactless check-in.\",\n      color: \"from-purple-500 to-pink-500\"\n    },\n    {\n      icon: BarChart3,\n      title: \"AI-Powered Analytics\",\n      description: \"Real-time insights, attendance predictions, and automated post-event reports with actionable recommendations.\",\n      color: \"from-green-500 to-emerald-500\"\n    },\n    {\n      icon: Smartphone,\n      title: \"Mobile-First Design\",\n      description: \"Native app experience with offline functionality, push notifications, and seamless cross-device sync.\",\n      color: \"from-orange-500 to-red-500\"\n    }\n  ]\n\n  const testimonials = [\n    {\n      name: \"<PERSON>\",\n      role: \"Corporate Event Manager\",\n      company: \"TechCorp Inc.\",\n      quote: \"EventFlow Pro transformed our chaotic event planning into a streamlined process. We've reduced planning time by 70% and increased attendance by 40%.\",\n      before: \"Managing 15 spreadsheets, constant email chains, manual check-ins taking 2 hours\",\n      after: \"One unified dashboard, automated workflows, 5-minute check-in process\"\n    },\n    {\n      name: \"Michael Rodriguez\",\n      role: \"Conference Organizer\",\n      company: \"Global Events Ltd.\",\n      quote: \"The AI analytics helped us predict and prevent a 30% no-show rate. The insights are incredibly accurate and actionable.\",\n      before: \"Guessing attendance, last-minute venue changes, poor engagement tracking\",\n      after: \"95% attendance prediction accuracy, optimized venue selection, real-time engagement metrics\"\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-200 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Calendar className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">EventFlow Pro</span>\n            </div>\n            <div className=\"hidden md:flex items-center space-x-8\">\n              <a href=\"#features\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Features</a>\n              <a href=\"/demo\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Demo</a>\n              <a href=\"/dashboard\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Dashboard</a>\n              <Button variant=\"outline\">Sign In</Button>\n              <Button variant=\"gradient\" onClick={() => window.open('/demo', '_blank')}>\n                Try Demo\n              </Button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"pt-24 pb-16 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n            >\n              <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n                From Event <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600\">Chaos</span> to\n                <br />Seamless <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600\">Execution</span>\n              </h1>\n              <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n                Transform your corporate event management with AI-powered analytics, QR code ticketing,\n                and real-time dashboards. Say goodbye to spreadsheet chaos and hello to professional automation.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n                <Button size=\"lg\" variant=\"gradient\" className=\"text-lg px-8 py-4\" onClick={() => window.open('/demo', '_blank')}>\n                  Start Your Transformation\n                  <ArrowRight className=\"ml-2 w-5 h-5\" />\n                </Button>\n                <Button size=\"lg\" variant=\"outline\" className=\"text-lg px-8 py-4\" onClick={() => window.open('/demo', '_blank')}>\n                  <Play className=\"mr-2 w-5 h-5\" />\n                  Try Interactive Demo\n                </Button>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Problem/Solution Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n            >\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                The Reality of Corporate Event Management\n              </h2>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1\">\n                    <span className=\"text-red-600 text-sm\">✗</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">Spreadsheet Chaos</h3>\n                    <p className=\"text-gray-600\">Managing attendee lists across 15+ spreadsheets, constant version conflicts</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1\">\n                    <span className=\"text-red-600 text-sm\">✗</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">Manual Check-ins</h3>\n                    <p className=\"text-gray-600\">2-hour check-in queues, lost registrations, frustrated attendees</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1\">\n                    <span className=\"text-red-600 text-sm\">✗</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">No Real Insights</h3>\n                    <p className=\"text-gray-600\">Guessing attendance, no engagement tracking, weeks of manual reporting</p>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n            >\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                The EventFlow Pro Solution\n              </h2>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">Unified Dashboard</h3>\n                    <p className=\"text-gray-600\">One platform for everything - registration, ticketing, analytics, and communication</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">5-Minute Check-ins</h3>\n                    <p className=\"text-gray-600\">QR code scanning, instant verification, seamless attendee experience</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1\">\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">AI-Powered Insights</h3>\n                    <p className=\"text-gray-600\">Real-time analytics, attendance predictions, automated reports</p>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Everything You Need for Perfect Events\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              From creation to analytics, our platform handles every aspect of event management with intelligence and automation.\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"group\"\n              >\n                <Card className=\"h-full hover:shadow-lg transition-shadow duration-300\">\n                  <CardHeader>\n                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                      <feature.icon className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <CardTitle className=\"text-xl\">{feature.title}</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-gray-600\">\n                      {feature.description}\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div>\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <Calendar className=\"w-5 h-5 text-white\" />\n                </div>\n                <span className=\"text-xl font-bold\">EventFlow Pro</span>\n              </div>\n              <p className=\"text-gray-400\">\n                Transforming event management from chaos to seamless execution.\n              </p>\n            </div>\n            <div>\n              <h3 className=\"font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Features</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Pricing</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">API</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Integrations</a></li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">About</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Blog</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Careers</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Contact</a></li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"font-semibold mb-4\">Support</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Help Center</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Documentation</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Status</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Security</a></li>\n              </ul>\n            </div>\n          </div>\n          <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n            <p>&copy; 2024 EventFlow Pro. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAnBA;;;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,WAAW;QACf;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,kNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,8MAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAsD;;;;;;kDACpF,8OAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAAsD;;;;;;kDAChF,8OAAC;wCAAE,MAAK;wCAAa,WAAU;kDAAsD;;;;;;kDACrF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;kDAC1B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAW,SAAS,IAAM,OAAO,IAAI,CAAC,SAAS;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlF,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,8OAAC;oCAAG,WAAU;;wCAAoD;sDACrD,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;wCAAY;sDACpH,8OAAC;;;;;wCAAK;sDAAS,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;8CAE9G,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAW,WAAU;4CAAoB,SAAS,IAAM,OAAO,IAAI,CAAC,SAAS;;gDAAW;8DAEhH,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,WAAU;4CAAoB,SAAS,IAAM,OAAO,IAAI,CAAC,SAAS;;8DACpG,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAuB;;;;;;;;;;;kEAEzC,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAuB;;;;;;;;;;;kEAEzC,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAuB;;;;;;;;;;;kEAEzC,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3C,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAW,CAAC,sCAAsC,EAAE,QAAQ,KAAK,CAAC,8FAA8F,CAAC;kEACpK,cAAA,8OAAC,QAAQ,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAE1B,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;0DAE/C,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,QAAQ,WAAW;;;;;;;;;;;;;;;;;mCAhBrB;;;;;;;;;;;;;;;;;;;;;0BA2Bf,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;sDAEtC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAGnE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAGnE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAIrE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}