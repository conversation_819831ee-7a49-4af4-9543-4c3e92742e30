export interface EventAnalytics {
  totalRegistrations: number
  checkedInCount: number
  attendanceRate: number
  registrationTrend: Array<{ date: string; count: number }>
  checkInTrend: Array<{ time: string; count: number }>
  demographicData: {
    ageGroups: Array<{ group: string; count: number }>
    locations: Array<{ location: string; count: number }>
  }
  engagementMetrics: {
    averageSessionDuration: number
    peakAttendanceTime: string
    dropOffRate: number
  }
}

export interface AIInsights {
  attendancePrediction: {
    predicted: number
    confidence: number
    factors: string[]
  }
  recommendations: Array<{
    type: 'marketing' | 'logistics' | 'engagement' | 'timing'
    priority: 'high' | 'medium' | 'low'
    title: string
    description: string
    impact: string
  }>
  riskFactors: Array<{
    factor: string
    severity: 'high' | 'medium' | 'low'
    mitigation: string
  }>
  benchmarkComparison: {
    industryAverage: number
    yourPerformance: number
    percentile: number
  }
}

export async function generateEventAnalytics(eventId: string): Promise<EventAnalytics> {
  // In a real implementation, this would fetch data from your database
  // For demo purposes, we'll generate realistic mock data
  
  const mockAnalytics: EventAnalytics = {
    totalRegistrations: Math.floor(Math.random() * 500) + 100,
    checkedInCount: Math.floor(Math.random() * 400) + 80,
    attendanceRate: 0,
    registrationTrend: generateRegistrationTrend(),
    checkInTrend: generateCheckInTrend(),
    demographicData: {
      ageGroups: [
        { group: '18-25', count: Math.floor(Math.random() * 50) + 20 },
        { group: '26-35', count: Math.floor(Math.random() * 80) + 40 },
        { group: '36-45', count: Math.floor(Math.random() * 60) + 30 },
        { group: '46-55', count: Math.floor(Math.random() * 40) + 20 },
        { group: '55+', count: Math.floor(Math.random() * 30) + 10 }
      ],
      locations: [
        { location: 'New York', count: Math.floor(Math.random() * 100) + 50 },
        { location: 'California', count: Math.floor(Math.random() * 80) + 40 },
        { location: 'Texas', count: Math.floor(Math.random() * 60) + 30 },
        { location: 'Florida', count: Math.floor(Math.random() * 50) + 25 },
        { location: 'Other', count: Math.floor(Math.random() * 70) + 35 }
      ]
    },
    engagementMetrics: {
      averageSessionDuration: Math.floor(Math.random() * 120) + 60, // minutes
      peakAttendanceTime: '2:30 PM',
      dropOffRate: Math.random() * 0.3 + 0.1 // 10-40%
    }
  }

  mockAnalytics.attendanceRate = (mockAnalytics.checkedInCount / mockAnalytics.totalRegistrations) * 100

  return mockAnalytics
}

export async function generateAIInsights(analytics: EventAnalytics): Promise<AIInsights> {
  // Simulate AI analysis with realistic insights
  const insights: AIInsights = {
    attendancePrediction: {
      predicted: Math.floor(analytics.totalRegistrations * (0.8 + Math.random() * 0.2)),
      confidence: Math.floor(Math.random() * 20) + 75, // 75-95%
      factors: [
        'Historical attendance patterns',
        'Weather forecast',
        'Competing events',
        'Registration timing',
        'Marketing reach'
      ]
    },
    recommendations: generateRecommendations(analytics),
    riskFactors: generateRiskFactors(analytics),
    benchmarkComparison: {
      industryAverage: 78.5,
      yourPerformance: analytics.attendanceRate,
      percentile: Math.floor(Math.random() * 40) + 60 // 60-100th percentile
    }
  }

  return insights
}

function generateRegistrationTrend(): Array<{ date: string; count: number }> {
  const trend = []
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 30)

  for (let i = 0; i < 30; i++) {
    const date = new Date(startDate)
    date.setDate(date.getDate() + i)
    
    // Simulate increasing registrations closer to event
    const baseCount = Math.floor(Math.random() * 10) + 1
    const proximityMultiplier = (30 - i) / 30 * 2 + 0.5
    const count = Math.floor(baseCount * proximityMultiplier)
    
    trend.push({
      date: date.toISOString().split('T')[0],
      count
    })
  }

  return trend
}

function generateCheckInTrend(): Array<{ time: string; count: number }> {
  const trend = []
  const eventStart = 9 // 9 AM
  const eventEnd = 17 // 5 PM

  for (let hour = eventStart; hour <= eventEnd; hour++) {
    const time = `${hour}:00`
    let count = 0

    // Simulate check-in patterns
    if (hour === eventStart) {
      count = Math.floor(Math.random() * 50) + 30 // Heavy check-in at start
    } else if (hour === eventStart + 1) {
      count = Math.floor(Math.random() * 30) + 20 // Continued check-ins
    } else if (hour >= 12 && hour <= 13) {
      count = Math.floor(Math.random() * 20) + 10 // Lunch break arrivals
    } else {
      count = Math.floor(Math.random() * 15) + 5 // Scattered throughout
    }

    trend.push({ time, count })
  }

  return trend
}

function generateRecommendations(analytics: EventAnalytics): AIInsights['recommendations'] {
  const recommendations = []

  if (analytics.attendanceRate < 70) {
    recommendations.push({
      type: 'marketing',
      priority: 'high',
      title: 'Boost Last-Minute Marketing',
      description: 'Current attendance rate is below optimal. Consider targeted social media campaigns and email reminders.',
      impact: 'Could increase attendance by 15-25%'
    })
  }

  if (analytics.engagementMetrics.dropOffRate > 0.25) {
    recommendations.push({
      type: 'engagement',
      priority: 'medium',
      title: 'Improve Session Engagement',
      description: 'High drop-off rate detected. Consider interactive elements, Q&A sessions, or networking breaks.',
      impact: 'Reduce drop-off by 10-15%'
    })
  }

  recommendations.push({
    type: 'logistics',
    priority: 'medium',
    title: 'Optimize Check-in Process',
    description: 'Based on registration patterns, prepare for peak check-in times around event start.',
    impact: 'Reduce wait times by 40%'
  })

  return recommendations
}

function generateRiskFactors(analytics: EventAnalytics): AIInsights['riskFactors'] {
  const risks = []

  if (analytics.totalRegistrations > 400) {
    risks.push({
      factor: 'High Registration Volume',
      severity: 'medium',
      mitigation: 'Ensure adequate staffing and multiple check-in stations'
    })
  }

  if (analytics.attendanceRate < 60) {
    risks.push({
      factor: 'Low Predicted Attendance',
      severity: 'high',
      mitigation: 'Implement targeted re-engagement campaigns and follow-up communications'
    })
  }

  return risks
}

export function calculateROI(eventCost: number, revenue: number, attendeeValue: number): number {
  const totalValue = revenue + (attendeeValue * 0.1) // Assume 10% of attendee value is immediate
  return ((totalValue - eventCost) / eventCost) * 100
}

export function predictOptimalEventTime(historicalData: any[]): string {
  // Analyze historical data to suggest optimal event timing
  // For demo, return a recommendation
  return "Tuesday-Thursday, 2:00 PM - 4:00 PM shows highest engagement rates"
}
